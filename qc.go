package mnms

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"os"
	"runtime"
	"sync"
	"time"

	"github.com/denisbrodbeck/machineid"
	"github.com/gorilla/websocket"
	"github.com/gosnmp/gosnmp"
	"github.com/qeof/q"
)

// global context holder
type QContext struct {
	DevMutex                  sync.Mutex                         `json:"-"`
	StatisticsMutex           sync.Mutex                         `json:"-"`
	Name                      string                             `json:"name"`
	Kind                      string                             `json:"kind"`
	Port                      int                                `json:"port"`
	CmdIndex                  int                                `json:"cmd_index"`
	RootURL                   string                             `json:"root_url"`
	IsRoot                    bool                               `json:"is_root"`
	ConfigFile                string                             `json:"config_file"`
	DumpStackTrace            bool                               `json:"-"`
	DevData                   map[string]DevInfo                 `json:"-"`
	CmdMutex                  sync.Mutex                         `json:"-"`
	CmdData                   map[string]CmdInfo                 `json:"-"`
	ClientMutex               sync.Mutex                         `json:"-"`
	Clients                   map[string]ClientInfo              `json:"-"`
	Logs                      map[string]Log                     `json:"-"`
	OpenvpnData               map[string]OpenvpnKeys             `json:"-"`
	OpenvpnMutex              sync.Mutex                         `json:"-"`
	RemoteSyslogServer        net.Conn                           `json:"-"`
	RemoteSyslogServerAddr    string                             `json:"remote_syslog_server_addr"`
	SyslogLocalPath           string                             `json:"syslog_local_path"`
	SyslogFileSize            uint                               `json:"syslog_file_size"`
	SyslogCompress            bool                               `json:"syslog_compress"`
	SyslogBakAfterFwd         bool                               `json:"syslog_keep_copy"`
	SyslogSeverityRngFwd      SyslogSeverityRange                `json:"syslog_severity_rng_fwd"`
	MqttBrokerAddr            string                             `json:"mqtt_broker_addr"`
	SyslogServerAddr          string                             `json:"syslog_server_addr"`
	TrapServerAddr            string                             `json:"trap_server_addr"`
	WebSocketClient           map[*websocket.Conn]bool           `json:"-"`
	WebSocketMessageBroadcast chan WebSocketMessage              `json:"-"`
	CmdInterval               int                                `json:"cmd_interval"`
	RegisterInterval          int                                `json:"register_interval"`
	GwdInterval               int                                `json:"gwd_interval"`
	Domain                    string                             `json:"domain"`
	TopologyData              map[string]Topology                `json:"-"`
	TopologyMutex             sync.Mutex                         `json:"-"`
	AdminToken                string                             `json:"-"`
	OwnPublicKeys             []byte                             `json:"-"`
	SnmpOptions               SnmpOptions                        `json:"snmp_options"`
	WgMutex                   sync.Mutex                         `json:"-"`
	WgData                    map[string]WgInfo                  `json:"-"`
	TcpProxyData              map[string]map[string]TcpProxyInfo `json:"-"`
	TcpProxyMutex             sync.Mutex                         `json:"-"`
	NmsServiceURL             string                             `json:"-"`
	idpsMutex                 sync.Mutex                         `json:"-"`
	IdpsReport                map[string]IdpsRePortInfo          `json:"-"`
	Version                   string                             `json:"-"`
	SshConnectionsMutex       sync.Mutex                         `json:"-"`
	SshServerPort             int                                `json:"ssh_server_port"`
	SshServerKeyPath          string                             `json:"ssh_server_key_path"`
	SshConnections            map[int]SshConnection              `json:"-"`
	PortAndPowerInfo          map[string]PortAndPowerInfo        `json:"-"`
	PortAndPowerMutex         sync.Mutex                         `json:"-"`
	VerifyInterval            int                                `json:"verify_interval"`
	UpdateSvcInterval         int                                `json:"updatesvcinterval_interval"`
	ForwardSvcData            map[string]ForwardConfig           `json:"-"`
	ForwardMutex              sync.Mutex                         `json:"-"`
	AlertForwarder            *AlertForwarder                    `json:"-"`
	GroupData                 map[string]*Relm                   `json:"-"`
	GroupMutex                sync.Mutex                         `json:"-"`

	License      *NimblLicense `json:"license"`
	SvcStartTime int           `json:"svc_start_time"`
	WsMutex      sync.Mutex    `json:"-"`
}

// GetFirstServiceName returns the name of the service with the given kind.
func GetFirstServiceName(kind string) (string, error) {
	for k, v := range QC.Clients {
		if v.Kind == kind {
			return k, nil
		}
	}
	return "", fmt.Errorf("can't find %s type service", kind)
}

// MarshalJSON add numDevices and numClients
func (qc *QContext) MarshalJSON() ([]byte, error) {
	type Alias QContext
	return json.Marshal(&struct {
		NumDevices  int  `json:"num_devices"`
		NumClients  int  `json:"num_clients"`
		IdpsLicense bool `json:"idps_license"`
		*Alias
	}{
		NumDevices:  len(qc.DevData),
		NumClients:  len(qc.Clients),
		IdpsLicense: qc.License.HasFeatureIdps(),
		Alias:       (*Alias)(qc),
	})
}

var QC QContext

func init() {
	QC.Clients = make(map[string]ClientInfo) // list of non-root mnms services registered to root
	QC.Port = 27182                          // euler's number
	QC.MqttBrokerAddr = ":11883"             // ":1883"
	QC.SyslogServerAddr = ":5514"            // ":514"
	QC.TrapServerAddr = ":5162"              // ":162"
	QC.WebSocketMessageBroadcast = make(chan WebSocketMessage, 100)
	QC.WebSocketClient = make(map[*websocket.Conn]bool)
	QC.TopologyData = make(map[string]Topology)
	QC.CmdInterval = 5
	QC.RegisterInterval = 60
	QC.GwdInterval = 60
	QC.SyslogLocalPath = "syslog_mnms.log"
	QC.SyslogFileSize = 100 // megabytes
	QC.SyslogCompress = true
	QC.SyslogBakAfterFwd = false
	QC.SyslogSeverityRngFwd = SyslogSeverityRange{0, 7}
	QC.ConfigFile = "bbrootconfig.enc" // default configuration file name
	QC.SnmpOptions = SnmpOptions{
		Community: "public", // by default should be public since device by default comstring public for read all
		Version:   gosnmp.Version2c,
		Timeout:   time.Duration(2) * time.Second,
		Port:      161,
	}
	QC.License = &NimblLicense{
		FeatureAnomalyDetection: false,
		FeatureIdps:             false,
	}
	// QC.AnomalyLicense = false
	// QC.IdpsLicense = false
	QC.SvcStartTime = int(time.Now().Unix())
	QC.Version = "v1.0.4"
	QC.PortAndPowerInfo = make(map[string]PortAndPowerInfo)
	QC.GroupData = make(map[string]*Relm)

	// Load groups from config on startup
	err := GM.LoadGroupsFromConfig()
	if err != nil {
		q.Q("Failed to load groups from config:", err)
	}
}

// ClientInfo contains data about a client node instance in a cluster.
// wireguard public key will exchange between nodes to establish a VPN tunnel.
// VPN peer address, allowed IPs, and port will be assigned by the root node.
type ClientInfo struct {
	Name            string   `json:"name"` // name of the client node
	Kind            string   `json:"kind"` // kind of the client
	NumDevices      int      `json:"num_devices"`
	NumCmds         int      `json:"num_cmds"`
	NumLogsReceived int      `json:"num_logs_received"` // number of logs received from this client node
	NumLogsSent     int      `json:"num_logs_sent"`     // number of logs sent to this client node
	Start           int      `json:"start"`             // start time of this client node
	Now             int      `json:"now"`               // current time of this client node
	NumGoroutines   int      `json:"num_goroutines"`    // number of goroutines running on this client node
	IPAddresses     []string `json:"ip_addresses"`      // list of IP addresses of this client node
	Status          string   `json:"status"`
	MachineID       string   `json:"machine_id"`
	Parent          string   `json:"parent"` // parent of this node
}

func RegisterMain() {
	for {
		ips, err := GetLocalIP()
		if err != nil {
			ips = []string{"Unknown"}
		}

		machineId, err := machineid.ID()
		if err != nil {
			q.Q(err, "use hostname as key instead")
			hostname, err := os.Hostname()
			if err != nil {
				q.Q(err)
				hostname = "Unknown"
			}
			hash := sha256.Sum256([]byte(hostname))
			machineId = hex.EncodeToString(hash[:])
		}

		ci := ClientInfo{
			Name:            QC.Name,
			Kind:            QC.Kind,
			NumDevices:      len(QC.DevData),
			NumCmds:         len(QC.CmdData),
			NumLogsReceived: TotalLogsReceived,
			NumLogsSent:     TotalLogsSent,
			Start:           QC.SvcStartTime,
			Now:             int(time.Now().Unix()),
			NumGoroutines:   runtime.NumGoroutine(),
			IPAddresses:     ips,
			Status:          "active",
			MachineID:       machineId,
		}

		jsonBytes, err := json.Marshal(ci)
		if err != nil {
			q.Q(err)
			continue
		}
		resp, err := PostWithToken(QC.RootURL+"/api/v1/register",
			QC.AdminToken, bytes.NewBuffer(jsonBytes))
		if err != nil {
			q.Q(err)
		}
		if resp != nil {
			// save close, resp should not be nil here
			if resp.StatusCode == 500 {
				// print message while exit client
				bodyText, _ := io.ReadAll(resp.Body)
				fmt.Println(string(bodyText))
				DoExit(0)
			}
			resp.Body.Close()
		}

		time.Sleep(time.Duration(QC.RegisterInterval) * time.Second) // XXX
	}
}

func CheckNetworkServicesAlive(pollingInterval int) {
	pollingTimer := time.NewTicker(time.Second * time.Duration(pollingInterval))
	for range pollingTimer.C {
		IsNetworkServicesAlive()
	}
}

// MarshalQC returns a JSON representation of the QContext.
func MarshalQC() ([]byte, error) {
	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	QC.CmdMutex.Lock()
	defer QC.CmdMutex.Unlock()
	QC.ClientMutex.Lock()
	defer QC.ClientMutex.Unlock()
	QC.OpenvpnMutex.Lock()
	defer QC.OpenvpnMutex.Unlock()
	QC.TopologyMutex.Lock()
	defer QC.TopologyMutex.Unlock()

	jsonBytes, err := json.Marshal(&QC)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	return jsonBytes, nil
}

func IsNetworkServicesAlive() {
	QC.ClientMutex.Lock()
	defer QC.ClientMutex.Unlock()
	for _, client := range QC.Clients {
		if client.Status == "inactive" {
			continue
		}
		if int(time.Now().Unix())-client.Now > 121 {
			ci := client
			ci.Status = "inactive"
			QC.Clients[client.Name] = ci
			SendSyslog(LOG_ALERT, "service_inactive", fmt.Sprintf("service %s is inactive", client.Name))
		}
	}
}
