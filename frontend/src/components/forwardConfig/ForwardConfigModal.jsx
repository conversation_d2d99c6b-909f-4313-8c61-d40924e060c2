import React, { useState, useEffect, useMemo } from "react";
import {
	Modal,
	Form,
	Select,
	Checkbox,
	Input,
	InputNumber,
	Divider,
	Space,
	Typography,
	Tag,
	Button,
	notification,
	Collapse,
	Badge,
	Row,
	Col,
	Card,
	Alert,
	List,
} from "antd";
import { 
	PlusOutlined, 
	MinusCircleOutlined,
	WhatsAppOutlined,
	MessageOutlined,
	ApiOutlined,
	CheckCircleOutlined,
	CloseCircleOutlined,
	SettingOutlined,
	SendOutlined,
	ExperimentOutlined,
	SyncOutlined,
	InfoCircleOutlined
} from "@ant-design/icons";
import { useGetCommandResultQuery } from "../../app/services/commandApi";

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

const ForwardConfigModal = ({
	visible,
	onCancel,
	onOk,
	initialConfig,
	loading,
	selectedForwardService,
	sendCommand, // Add sendCommand prop
}) => {
	const [form] = Form.useForm();
	const [testForm] = Form.useForm();
	const [selectedForwarder, setSelectedForwarder] = useState("");
	const [formValues, setFormValues] = useState({});
	const [testSending, setTestSending] = useState(false);
	const [showTestSection, setShowTestSection] = useState(false);
	const [isFirstLoad, setIsFirstLoad] = useState(true); // Track if this is the first load
	const [testCommands, setTestCommands] = useState([]); // Store sent test commands
	const [selectedTestCommand, setSelectedTestCommand] = useState(""); // Currently selected command for result viewing
	const [showResultSection, setShowResultSection] = useState(false); // Control result section visibility

	useEffect(() => {
		if (initialConfig && visible && selectedForwardService) {
			setSelectedForwarder(selectedForwardService);
			form.resetFields();
			
			// Ensure all services have proper default structure before setting values
			const configToSet = { ...initialConfig };
			if (configToSet[selectedForwardService]) {
				const serviceConfig = configToSet[selectedForwardService];
				
				// Ensure WhatsApp has proper structure
				if (!serviceConfig.whatsapp) {
					serviceConfig.whatsapp = {
						enabled: false,
						account_sid: "",
						auth_token: "",
						from_number: "",
						to_numbers: [],
						alert_config: {
							min_severity: 0,
							max_severity: 7,
							rate_limit_seconds: 300,
							max_alerts_per_minute: 5,
							keywords: [],
							exclude_keywords: [],
						},
					};
				}
				
				// Ensure Telegram has proper structure
				if (!serviceConfig.telegram) {
					serviceConfig.telegram = {
						enabled: false,
						bot_token: "",
						chat_ids: [],
						alert_config: {
							min_severity: 0,
							max_severity: 7,
							rate_limit_seconds: 180,
							max_alerts_per_minute: 10,
							keywords: [],
							exclude_keywords: [],
						},
					};
				}
				
				// Ensure MQTT has proper structure
				if (!serviceConfig.mqtt) {
					serviceConfig.mqtt = {
						enabled: false,
						broker_host: "localhost",
						broker_port: 1883,
						username: "",
						password: "",
						topic: "mnms/alerts",
						qos: 1,
						retain: false,
						alert_config: {
							min_severity: 0,
							max_severity: 7,
							rate_limit_seconds: 60,
							max_alerts_per_minute: 20,
							keywords: [],
							exclude_keywords: [],
						},
					};
				}
			}
			
			form.setFieldsValue(configToSet);
			setFormValues(configToSet);
			setIsFirstLoad(true); // Mark as first load
		}
	}, [initialConfig, visible, selectedForwardService, form]);

	// Reset test state when modal is closed
	useEffect(() => {
		if (!visible) {
			setTestCommands([]);
			setSelectedTestCommand("");
			setShowTestSection(false);
			setShowResultSection(false);
		}
	}, [visible]);

	// Handle form value changes to update headers in real-time
	const handleFormChange = (changedValues, allValues) => {
		setFormValues(allValues);
		setIsFirstLoad(false); // Mark that user has made changes
	};

	// Handle test sending
	const handleTestSend = async () => {
		try {
			setTestSending(true);
			
			// Get test form values
			const testValues = await testForm.validateFields();
			
			// Get current form config - try form first, then fall back to initialConfig
			const currentConfig = form.getFieldsValue();
			let forwarderConfig = currentConfig[selectedForwarder];
			
			// If form doesn't have the data yet (first time opening), use initialConfig
			if (!forwarderConfig && initialConfig && initialConfig[selectedForwarder]) {
				forwarderConfig = initialConfig[selectedForwarder];
			}
			
			if (!forwarderConfig) {
				notification.error({ message: "No configuration found to test" });
				return;
			}

			// Prepare the config for the command (same structure as used in save)
			const configToSend = {
				whatsapp: forwarderConfig.whatsapp || {
					enabled: false,
					account_sid: "",
					auth_token: "",
					from_number: "",
					to_numbers: [],
					alert_config: {
						min_severity: 0,
						max_severity: 7,
						rate_limit_seconds: 300,
						max_alerts_per_minute: 5,
						keywords: [],
						exclude_keywords: [],
					},
				},
				telegram: forwarderConfig.telegram || {
					enabled: false,
					bot_token: "",
					chat_ids: [],
					alert_config: {
						min_severity: 0,
						max_severity: 7,
						rate_limit_seconds: 180,
						max_alerts_per_minute: 10,
						keywords: [],
						exclude_keywords: [],
					},
				},
				mqtt: forwarderConfig.mqtt || {
					enabled: false,
					broker_host: "localhost",
					broker_port: 1883,
					username: "",
					password: "",
					topic: "mnms/alerts",
					qos: 1,
					retain: false,
					alert_config: {
						min_severity: 0,
						max_severity: 7,
						rate_limit_seconds: 60,
						max_alerts_per_minute: 20,
						keywords: [],
						exclude_keywords: [],
					},
				},
			};

			const jsonString = JSON.stringify(configToSend);
			const commandStr = `forward custom send ${jsonString} ${testValues.facility} ${testValues.severity} ${testValues.tag} ${testValues.message}`;
			
			const command = [
				{ 
					command: commandStr, 
					client: selectedForwarder 
				},
			];
			
			const result = await sendCommand(command).unwrap();
			
			// Store the latest command (replace previous ones for cleaner UI)
			setTestCommands([commandStr]);
			setSelectedTestCommand(commandStr);
			setShowResultSection(true);
			
			notification.success({ 
				message: "Test message sent successfully!",
				description: "Check your configured channels for the test alert. You can view the result in the Response Result section below.",
				duration: 5
			});
			
		} catch (error) {
			notification.error({ 
				message: "Test failed", 
				description: error.data?.error || error.message || "Failed to send test message"
			});
		} finally {
			setTestSending(false);
		}
	};

	const handleOk = async () => {
		try {
			const allValues = form.getFieldsValue();
			const selectedForwarderConfig = allValues[selectedForwarder];
			
			if (!selectedForwarderConfig) {
				notification.error({ message: "No forwarder configuration found" });
				return;
			}

			// Use getFieldsValue with true to get ALL form values including collapsed panels
			const completeValues = form.getFieldsValue(true);
			// console.log("Saving config - Complete form values:", completeValues);
			// console.log("Saving config - Initial config for comparison:", initialConfig?.[selectedForwarder]);
			
			// Create final config by merging form data with initial config as fallback
			const finalConfig = {
				[selectedForwarder]: {
					whatsapp: {
						// Start with initial config
						...(initialConfig?.[selectedForwarder]?.whatsapp || {}),
						// Override with form values if they exist
						...(completeValues[selectedForwarder]?.whatsapp || {})
					},
					telegram: {
						// Start with initial config
						...(initialConfig?.[selectedForwarder]?.telegram || {}),
						// Override with form values if they exist
						...(completeValues[selectedForwarder]?.telegram || {})
					},
					mqtt: {
						// Start with initial config
						...(initialConfig?.[selectedForwarder]?.mqtt || {}),
						// Override with form values if they exist
						...(completeValues[selectedForwarder]?.mqtt || {})
					}
				}
			};
			
			// console.log("Final config being sent:", finalConfig);

			// Validate only if services are enabled
			const fieldsToValidate = [];
			const configToValidate = finalConfig[selectedForwarder];
			
			if (configToValidate.whatsapp?.enabled) {
				fieldsToValidate.push(
					[selectedForwarder, "whatsapp", "account_sid"],
					[selectedForwarder, "whatsapp", "auth_token"],
					[selectedForwarder, "whatsapp", "from_number"]
				);
			}
			
			if (configToValidate.telegram?.enabled) {
				fieldsToValidate.push(
					[selectedForwarder, "telegram", "bot_token"]
				);
			}
			
			if (configToValidate.mqtt?.enabled) {
				fieldsToValidate.push(
					[selectedForwarder, "mqtt", "broker_host"],
					[selectedForwarder, "mqtt", "broker_port"],
					[selectedForwarder, "mqtt", "topic"]
				);
			}

			if (fieldsToValidate.length > 0) {
				await form.validateFields(fieldsToValidate);
			}

			onOk(finalConfig);
		} catch (error) {
			notification.error({ message: "Please fix form errors before submitting" });
		}
	};

	// Watch form changes more reliably
	const watchedValues = Form.useWatch([], form);

	// Helper function to get service status and summary
	const getServiceSummary = (serviceName) => {
		let serviceConfig = null;
		
		// On first load, use initialConfig directly from API response
		if (isFirstLoad && initialConfig && initialConfig[selectedForwarder]?.[serviceName]) {
			serviceConfig = initialConfig[selectedForwarder][serviceName];
		}
		// After user makes changes, use the form values
		else {
			// Try multiple sources for form values in order of preference
			// 1. Try watchedValues first (most current)
			if (watchedValues && watchedValues[selectedForwarder]?.[serviceName]) {
				serviceConfig = watchedValues[selectedForwarder][serviceName];
			}
			// 2. Try formValues state
			else if (formValues && formValues[selectedForwarder]?.[serviceName]) {
				serviceConfig = formValues[selectedForwarder][serviceName];
			}
			// 3. Try direct form access
			else if (form) {
				const formData = form.getFieldsValue();
				serviceConfig = formData[selectedForwarder]?.[serviceName];
			}
		}
		
		return getServiceSummaryFromConfig(serviceName, serviceConfig);
	};

	// Extract service summary logic into separate function
	const getServiceSummaryFromConfig = (serviceName, serviceConfig) => {
		// Check if service config exists and is enabled
		const enabled = serviceConfig?.enabled === true;
		let summary = "";

		if (!serviceConfig) {
			return { enabled: false, summary: "Not configured" };
		}

		switch (serviceName) {
			case "whatsapp":
				if (enabled) {
					const fromNumber = serviceConfig.from_number || "Not set";
					const toCount = serviceConfig.to_numbers?.length || 0;
					summary = `From: ${fromNumber}, To: ${toCount} numbers`;
				} else {
					const hasConfig = serviceConfig.account_sid || serviceConfig.from_number;
					summary = hasConfig ? "Configured but disabled" : "Disabled";
				}
				break;
			case "telegram":
				if (enabled) {
					const chatCount = serviceConfig.chat_ids?.length || 0;
					const hasToken = serviceConfig.bot_token ? "Token set" : "No token";
					summary = `${hasToken}, ${chatCount} chats`;
				} else {
					const hasConfig = serviceConfig.bot_token || (serviceConfig.chat_ids?.length > 0);
					summary = hasConfig ? "Configured but disabled" : "Disabled";
				}
				break;
			case "mqtt":
				if (enabled) {
					const broker = serviceConfig.broker_host || "localhost";
					const port = serviceConfig.broker_port || 1883;
					const topic = serviceConfig.topic || "mnms/alerts";
					summary = `${broker}:${port}/${topic}`;
				} else {
					const hasConfig = serviceConfig.broker_host || serviceConfig.topic;
					summary = hasConfig ? "Configured but disabled" : "Disabled";
				}
				break;
			default:
				summary = enabled ? "Enabled" : "Disabled";
		}

		return { enabled, summary };
	};

	// Generate panel header with status and summary
	const generatePanelHeader = (serviceName, icon, color) => {
		const { enabled, summary } = getServiceSummary(serviceName);
		const statusIcon = enabled ? <CheckCircleOutlined style={{ color: "#52c41a" }} /> : <CloseCircleOutlined style={{ color: "#ff4d4f" }} />;
		
		return (
			<Row justify="space-between" align="middle" style={{ width: "100%" }}>
				<Col>
					<Space>
						{icon}
						<Title level={5} style={{ margin: 0, textTransform: "capitalize" }}>
							{serviceName}
						</Title>
						{statusIcon}
						<Badge 
							status={enabled ? "processing" : "default"} 
							color={enabled ? color : "gray"}
						/>
					</Space>
				</Col>
				<Col>
					<Text type="secondary" style={{ fontSize: "12px" }}>
						{summary}
					</Text>
				</Col>
			</Row>
		);
	};

	// Generate panel headers that update when form values change
	const panelHeaders = useMemo(() => {
		// Force re-calculation by watching form values
		return {
			whatsapp: generatePanelHeader("whatsapp", <WhatsAppOutlined style={{ color: "#25D366" }} />, "#25D366"),
			telegram: generatePanelHeader("telegram", <MessageOutlined style={{ color: "#0088cc" }} />, "#0088cc"),
			mqtt: generatePanelHeader("mqtt", <ApiOutlined style={{ color: "#ff7300" }} />, "#ff7300")
		};
	}, [formValues, selectedForwarder, watchedValues, visible, initialConfig, isFirstLoad]); // Use isFirstLoad instead of headerRefresh

	const renderAlertConfig = (service) => (
		<Card size="small" title="Alert Configuration" style={{ marginTop: 16 }}>
			<Row gutter={16}>
				<Col span={12}>
					<Form.Item
						label="Min Severity"
						name={[selectedForwarder, service, "alert_config", "min_severity"]}
					>
						<Select placeholder="Select minimum severity" size="small">
							<Option value={0}>0 - Emergency</Option>
							<Option value={1}>1 - Alert</Option>
							<Option value={2}>2 - Critical</Option>
							<Option value={3}>3 - Error</Option>
							<Option value={4}>4 - Warning</Option>
							<Option value={5}>5 - Notice</Option>
							<Option value={6}>6 - Informational</Option>
							<Option value={7}>7 - Debug</Option>
						</Select>
					</Form.Item>
				</Col>
				<Col span={12}>
					<Form.Item
						label="Max Severity"
						name={[selectedForwarder, service, "alert_config", "max_severity"]}
					>
						<Select placeholder="Select maximum severity" size="small">
							<Option value={0}>0 - Emergency</Option>
							<Option value={1}>1 - Alert</Option>
							<Option value={2}>2 - Critical</Option>
							<Option value={3}>3 - Error</Option>
							<Option value={4}>4 - Warning</Option>
							<Option value={5}>5 - Notice</Option>
							<Option value={6}>6 - Informational</Option>
							<Option value={7}>7 - Debug</Option>
						</Select>
					</Form.Item>
				</Col>
			</Row>

			<Row gutter={16}>
				<Col span={12}>
					<Form.Item
						label="Rate Limit (seconds)"
						name={[selectedForwarder, service, "alert_config", "rate_limit_seconds"]}
					>
						<InputNumber min={1} placeholder="300" style={{ width: "100%" }} size="small" />
					</Form.Item>
				</Col>
				<Col span={12}>
					<Form.Item
						label="Max Alerts Per Minute"
						name={[selectedForwarder, service, "alert_config", "max_alerts_per_minute"]}
					>
						<InputNumber min={1} placeholder="5" style={{ width: "100%" }} size="small" />
					</Form.Item>
				</Col>
			</Row>

			<Form.Item label="Keywords (Include)">
				<Form.List name={[selectedForwarder, service, "alert_config", "keywords"]}>
					{(fields, { add, remove }) => (
						<>
							{fields.map(({ key, name, ...restField }) => (
								<Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
									<Form.Item
										{...restField}
										name={[name]}
										style={{ margin: 0 }}
									>
										<Input placeholder="error, critical, etc." size="small" />
									</Form.Item>
									<MinusCircleOutlined onClick={() => remove(name)} />
								</Space>
							))}
							<Form.Item style={{ margin: 0 }}>
								<Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />} size="small">
									Add Keyword
								</Button>
							</Form.Item>
						</>
					)}
				</Form.List>
			</Form.Item>

			<Form.Item label="Exclude Keywords">
				<Form.List name={[selectedForwarder, service, "alert_config", "exclude_keywords"]}>
					{(fields, { add, remove }) => (
						<>
							{fields.map(({ key, name, ...restField }) => (
								<Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
									<Form.Item
										{...restField}
										name={[name]}
										style={{ margin: 0 }}
									>
										<Input placeholder="debug, info, etc." size="small" />
									</Form.Item>
									<MinusCircleOutlined onClick={() => remove(name)} />
								</Space>
							))}
							<Form.Item style={{ margin: 0 }}>
								<Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />} size="small">
									Add Exclude Keyword
								</Button>
							</Form.Item>
						</>
					)}
				</Form.List>
			</Form.Item>
		</Card>
	);

	const renderWhatsAppConfig = () => (
		<Space direction="vertical" style={{ width: "100%" }}>
			<Form.Item
				name={[selectedForwarder, "whatsapp", "enabled"]}
				valuePropName="checked"
			>
				<Checkbox>Enable WhatsApp forwarding</Checkbox>
			</Form.Item>

			<Row gutter={16}>
				<Col span={12}>
					<Form.Item
						label="Account SID"
						name={[selectedForwarder, "whatsapp", "account_sid"]}
						rules={[{
							required: false,
							message: "Account SID is required when WhatsApp is enabled"
						}]}
					>
						<Input placeholder="Enter Twilio Account SID" />
					</Form.Item>
				</Col>
				<Col span={12}>
					<Form.Item
						label="Auth Token"
						name={[selectedForwarder, "whatsapp", "auth_token"]}
						rules={[{
							required: false,
							message: "Auth Token is required when WhatsApp is enabled"
						}]}
					>
						<Input.Password placeholder="Enter Twilio Auth Token" />
					</Form.Item>
				</Col>
			</Row>

			<Form.Item
				label="From Number"
				name={[selectedForwarder, "whatsapp", "from_number"]}
				rules={[{
					required: false,
					message: "From Number is required when WhatsApp is enabled"
				}]}
			>
				<Input placeholder="+***********" />
			</Form.Item>

			<Form.Item label="To Numbers">
				<Form.List name={[selectedForwarder, "whatsapp", "to_numbers"]}>
					{(fields, { add, remove }) => (
						<>
							{fields.map(({ key, name, ...restField }) => (
								<Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
									<Form.Item
										{...restField}
										name={[name]}
										rules={[{ required: false, message: "Phone number is required" }]}
										style={{ margin: 0, flex: 1 }}
									>
										<Input placeholder="+1234567890" />
									</Form.Item>
									<MinusCircleOutlined onClick={() => remove(name)} />
								</Space>
							))}
							<Form.Item style={{ margin: 0 }}>
								<Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
									Add Phone Number
								</Button>
							</Form.Item>
						</>
					)}
				</Form.List>
			</Form.Item>

			{renderAlertConfig("whatsapp")}
		</Space>
	);

	const renderTelegramConfig = () => (
		<Space direction="vertical" style={{ width: "100%" }}>
			<Form.Item
				name={[selectedForwarder, "telegram", "enabled"]}
				valuePropName="checked"
			>
				<Checkbox>Enable Telegram forwarding</Checkbox>
			</Form.Item>

			<Form.Item
				label="Bot Token"
				name={[selectedForwarder, "telegram", "bot_token"]}
				rules={[{
					required: false,
					message: "Bot Token is required when Telegram is enabled"
				}]}
			>
				<Input.Password placeholder="Enter Telegram Bot Token" />
			</Form.Item>

			<Form.Item label="Chat IDs">
				<Form.List name={[selectedForwarder, "telegram", "chat_ids"]}>
					{(fields, { add, remove }) => (
						<>
							{fields.map(({ key, name, ...restField }) => (
								<Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
									<Form.Item
										{...restField}
										name={[name]}
										rules={[{ required: false, message: "Chat ID is required" }]}
										style={{ margin: 0, flex: 1 }}
									>
										<Input placeholder="123456789" />
									</Form.Item>
									<MinusCircleOutlined onClick={() => remove(name)} />
								</Space>
							))}
							<Form.Item style={{ margin: 0 }}>
								<Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
									Add Chat ID
								</Button>
							</Form.Item>
						</>
					)}
				</Form.List>
			</Form.Item>

			{renderAlertConfig("telegram")}
		</Space>
	);

	const renderMqttConfig = () => (
		<Space direction="vertical" style={{ width: "100%" }}>
			<Form.Item
				name={[selectedForwarder, "mqtt", "enabled"]}
				valuePropName="checked"
			>
				<Checkbox>Enable MQTT forwarding</Checkbox>
			</Form.Item>

			<Row gutter={16}>
				<Col span={16}>
					<Form.Item
						label="Broker Host"
						name={[selectedForwarder, "mqtt", "broker_host"]}
						rules={[{
							required: false,
							message: "Broker Host is required when MQTT is enabled"
						}]}
					>
						<Input placeholder="localhost" />
					</Form.Item>
				</Col>
				<Col span={8}>
					<Form.Item
						label="Broker Port"
						name={[selectedForwarder, "mqtt", "broker_port"]}
						rules={[{
							required: false,
							message: "Broker Port is required when MQTT is enabled"
						}]}
					>
						<InputNumber placeholder="1883" min={1} max={65535} style={{ width: "100%" }} />
					</Form.Item>
				</Col>
			</Row>

			<Row gutter={16}>
				<Col span={12}>
					<Form.Item
						label="Username"
						name={[selectedForwarder, "mqtt", "username"]}
					>
						<Input placeholder="Username (optional)" />
					</Form.Item>
				</Col>
				<Col span={12}>
					<Form.Item
						label="Password"
						name={[selectedForwarder, "mqtt", "password"]}
					>
						<Input.Password placeholder="Password (optional)" />
					</Form.Item>
				</Col>
			</Row>

			<Row gutter={16}>
				<Col span={16}>
					<Form.Item
						label="Topic"
						name={[selectedForwarder, "mqtt", "topic"]}
						rules={[{
							required: false,
							message: "Topic is required when MQTT is enabled"
						}]}
					>
						<Input placeholder="mnms/alerts" />
					</Form.Item>
				</Col>
				<Col span={8}>
					<Form.Item
						label="QoS"
						name={[selectedForwarder, "mqtt", "qos"]}
					>
						<Select placeholder="Select QoS level">
							<Option value={0}>0 - At most once</Option>
							<Option value={1}>1 - At least once</Option>
							<Option value={2}>2 - Exactly once</Option>
						</Select>
					</Form.Item>
				</Col>
			</Row>

			<Form.Item
				name={[selectedForwarder, "mqtt", "retain"]}
				valuePropName="checked"
			>
				<Checkbox>Retain messages</Checkbox>
			</Form.Item>

			{renderAlertConfig("mqtt")}
		</Space>
	);

	// Query for command result when a test command is selected
	const { 
		data: commandResult, 
		refetch: refetchCommandResult 
	} = useGetCommandResultQuery(
		{ cValue: selectedTestCommand }, 
		{ skip: !selectedTestCommand }
	);

	// Handle viewing command result
	const handleViewResult = (commandStr) => {
		setSelectedTestCommand(commandStr);
		refetchCommandResult();
	};

	// Auto-refresh results for running commands
	useEffect(() => {
		if (commandResult && selectedTestCommand) {
			const resultData = Object.values(commandResult);
			if (resultData.length > 0) {
				const hasRunningCommand = resultData.some(
					(el) =>
						el.status === "" ||
						el.status === "running" ||
						el.status.includes("pending:") ||
						el.status.includes("info:")
				);
				
				if (hasRunningCommand) {
					const timeout = setTimeout(() => {
						refetchCommandResult();
					}, 5000);
					
					return () => clearTimeout(timeout);
				}
			}
		}
	}, [commandResult, selectedTestCommand, refetchCommandResult]);

	return (
		<Modal
			title={
				<Space>
					<SettingOutlined />
					Configure Log Forwarding: {selectedForwardService}
				</Space>
			}
			open={visible}
			onCancel={onCancel}
			onOk={handleOk}
			width={900}
			confirmLoading={loading}
			bodyStyle={{ padding: "20px" }}
		>
			<Form
				form={form}
				layout="vertical"
				initialValues={{}}
				onValuesChange={handleFormChange}
			>
				{selectedForwarder && (
					<>
						<Card 
							size="small" 
							title={`Forward Service: ${selectedForwarder}`}
							style={{ marginBottom: 16 }}
						>
							<Text type="secondary">
								Configure notification channels for log forwarding. 
								Enable the services you want to use and expand each section to configure.
							</Text>
						</Card>

						<Collapse 
							defaultActiveKey={[]} 
							ghost
							size="large"
						>
							<Panel 
								header={panelHeaders.whatsapp}
								key="whatsapp"
								forceRender={true}
							>
								{renderWhatsAppConfig()}
							</Panel>
							
							<Panel 
								header={panelHeaders.telegram}
								key="telegram"
								forceRender={true}
							>
								{renderTelegramConfig()}
							</Panel>
							
							<Panel 
								header={panelHeaders.mqtt}
								key="mqtt"
								forceRender={true}
							>
								{renderMqttConfig()}
							</Panel>
						</Collapse>

						{/* Test Configuration Section */}
						<Card 
							size="small" 
							title={
								<Space>
									<ExperimentOutlined />
									Test Configuration
								</Space>
							}
							style={{ marginTop: 16 }}
						>
							<Alert
								message="Test your forward configuration"
								description="Send a test message using your current configuration to verify it works before saving."
								type="info"
								showIcon
								style={{ marginBottom: 16 }}
							/>
							
							{!showTestSection ? (
								<Button 
									type="dashed" 
									icon={<ExperimentOutlined />}
									onClick={() => setShowTestSection(true)}
									block
								>
									Show Test Section
								</Button>
							) : (
								<>
									<Form
										form={testForm}
										layout="vertical"
										initialValues={{
											facility: 16, // Local use (16)
											severity: 4,  // Warning (4)
											tag: "test-alert",
											message: "This is a test alert from MNMS forward configuration"
										}}
									>
										<Row gutter={16}>
											<Col span={8}>
												<Form.Item
													label="Facility"
													name="facility"
													rules={[{ required: true, message: "Facility is required" }]}
													tooltip="Syslog facility (0-23). 16=Local use"
												>
													<Select>
														<Option value={0}>0 - Kernel messages</Option>
														<Option value={1}>1 - User-level messages</Option>
														<Option value={2}>2 - Mail system</Option>
														<Option value={3}>3 - System daemons</Option>
														<Option value={4}>4 - Security/authorization</Option>
														<Option value={5}>5 - Syslogd messages</Option>
														<Option value={6}>6 - Line printer subsystem</Option>
														<Option value={7}>7 - Network news subsystem</Option>
														<Option value={8}>8 - UUCP subsystem</Option>
														<Option value={9}>9 - Clock daemon</Option>
														<Option value={10}>10 - Security/authorization</Option>
														<Option value={11}>11 - FTP daemon</Option>
														<Option value={12}>12 - NTP subsystem</Option>
														<Option value={13}>13 - Log audit</Option>
														<Option value={14}>14 - Log alert</Option>
														<Option value={15}>15 - Clock daemon</Option>
														<Option value={16}>16 - Local use 0</Option>
														<Option value={17}>17 - Local use 1</Option>
														<Option value={18}>18 - Local use 2</Option>
														<Option value={19}>19 - Local use 3</Option>
														<Option value={20}>20 - Local use 4</Option>
														<Option value={21}>21 - Local use 5</Option>
														<Option value={22}>22 - Local use 6</Option>
														<Option value={23}>23 - Local use 7</Option>
													</Select>
												</Form.Item>
											</Col>
											<Col span={8}>
												<Form.Item
													label="Severity"
													name="severity"
													rules={[{ required: true, message: "Severity is required" }]}
													tooltip="Syslog severity (0-7). 0=Emergency, 7=Debug"
												>
													<Select>
														<Option value={0}>0 - Emergency</Option>
														<Option value={1}>1 - Alert</Option>
														<Option value={2}>2 - Critical</Option>
														<Option value={3}>3 - Error</Option>
														<Option value={4}>4 - Warning</Option>
														<Option value={5}>5 - Notice</Option>
														<Option value={6}>6 - Informational</Option>
														<Option value={7}>7 - Debug</Option>
													</Select>
												</Form.Item>
											</Col>
											<Col span={8}>
												<Form.Item
													label="Tag"
													name="tag"
													rules={[{ required: true, message: "Tag is required" }]}
													tooltip="Syslog tag/program name"
												>
													<Input placeholder="test-alert" />
												</Form.Item>
											</Col>
										</Row>
										
										<Form.Item
											label="Message"
											name="message"
											rules={[{ required: true, message: "Message is required" }]}
											tooltip="The alert message content"
										>
											<Input.TextArea 
												placeholder="This is a test alert from MNMS forward configuration"
												rows={2}
											/>
										</Form.Item>
									</Form>
									
									<Row gutter={8}>
										<Col>
											<Button 
												type="primary"
												icon={<SendOutlined />}
												onClick={handleTestSend}
												loading={testSending}
											>
												Send Test Alert
											</Button>
										</Col>
										<Col>
											<Button 
												onClick={() => setShowTestSection(false)}
											>
												Hide Test Section
											</Button>
										</Col>
									</Row>
								</>
							)}
						</Card>

						{/* Response Result Section */}
						{testCommands.length > 0 && (
							<Card 
								size="small" 
								title={
									<Space>
										<SyncOutlined />
										Response Result
									</Space>
								}
								style={{ marginTop: 16 }}
							>
								<Alert
									message="Test command result"
									description="View the status and result of your test alert below."
									type="info"
									showIcon
									style={{ marginBottom: 16 }}
								/>
								
								{!showResultSection ? (
									<Button 
										type="dashed" 
										icon={<SyncOutlined />}
										onClick={() => {
											setShowResultSection(true);
											handleViewResult(testCommands[0]);
										}}
										block
									>
										Show Response Result
									</Button>
								) : (
									<>
										{selectedTestCommand && commandResult ? (
											<div>
												{Object.values(commandResult).map(
													(v) =>
														v.command === selectedTestCommand && (
															<Space
																key={v.command}
																direction="vertical"
																style={{ width: "100%" }}
															>
																{/* Status Section */}
																<Row align="middle" style={{ marginBottom: 12 }}>
																	<Col span={4}>
																		<Text strong style={{ fontSize: '14px' }}>Status:</Text>
																	</Col>
																	<Col span={20}>
																		{v.status === "" ||
																		v.status === "running" ||
																		v.status.includes("pending:") ? (
																			<Tag
																				icon={<SyncOutlined spin />}
																				color="processing"
																			>
																				processing
																			</Tag>
																		) : v.status === "ok" ? (
																			<Tag
																				icon={<CheckCircleOutlined />}
																				color="success"
																			>
																				ok
																			</Tag>
																		) : v.status.includes("info:") ? (
																			<Tag
																				icon={<InfoCircleOutlined />}
																				color="#108ee9"
																			>
																				{v.status}
																			</Tag>
																		) : (
																			<Tag
																				icon={<CloseCircleOutlined />}
																				color="error"
																			>
																				{v.status}
																			</Tag>
																		)}
																	</Col>
																</Row>

																{/* Result Section */}
																<Row align="top" style={{ marginBottom: 12 }}>
																	<Col span={4}>
																		<Text strong style={{ fontSize: '14px' }}>Result:</Text>
																	</Col>
																	<Col span={20}>
																		{v.result !== "" ? (
																			(() => {
																				try {
																					const resultObj = JSON.parse(v.result);
																					
																					// Function to get friendly label for JSON keys
																					const getFriendlyLabel = (key) => {
																						const labelMap = {
																							'whatsapp_result': 'WhatsApp',
																							'telegram_result': 'Telegram',
																							'mqtt_result': 'MQTT',
																							'raw_line': 'Raw Log Entry'
																						};
																						return labelMap[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
																					};

																					return (
																						<div style={{ 
																							background: '#fafafa', 
																							padding: '16px', 
																							borderRadius: '8px', 
																							border: '1px solid #e8e8e8' 
																						}}>
																							{Object.entries(resultObj).map(([key, value], index) => (
																								<Row key={key} align="middle" style={{ 
																									marginBottom: index < Object.entries(resultObj).length - 1 ? '12px' : '0' 
																								}}>
																									<Col span={6}>
																										<Text strong style={{ 
																											color: '#1890ff',
																											fontSize: '13px'
																										}}>
																											{getFriendlyLabel(key)}:
																										</Text>
																									</Col>
																									<Col span={18}>
																										<Text style={{ 
																											color: key.includes('success') || value.includes('successfully') 
																												? '#52c41a' 
																												: key.includes('error') || key.includes('disabled') || value.includes('disabled') 
																												? '#ff4d4f' 
																												: '#595959',
																											fontSize: '13px',
																											fontFamily: key === 'raw_line' ? 'monospace' : 'inherit'
																										}}>
																											{value}
																										</Text>
																									</Col>
																								</Row>
																							))}
																						</div>
																					);
																				} catch (e) {
																					// If not valid JSON, display as plain text
																					return (
																						<Text style={{ 
																							fontFamily: 'monospace',
																							background: '#fafafa',
																							padding: '12px',
																							borderRadius: '6px',
																							border: '1px solid #e8e8e8',
																							display: 'block'
																						}}>
																							{v.result}
																						</Text>
																					);
																				}
																			})()
																		) : (
																			<Text type="secondary">NO result assigned</Text>
																		)}
																	</Col>
																</Row>
															</Space>
														)
												)}
											</div>
										) : (
											<Row align="middle" style={{ marginBottom: 12 }}>
												<Col span={4}>
													<Text strong style={{ fontSize: '14px' }}>Result:</Text>
												</Col>
												<Col span={20}>
													<Text type="secondary">Loading...</Text>
												</Col>
											</Row>
										)}
										
										<Row gutter={8} style={{ marginTop: 16 }}>
											<Col>
												<Button 
													type="primary"
													icon={<SyncOutlined />}
													onClick={() => handleViewResult(selectedTestCommand)}
													size="small"
												>
													Refresh Result
												</Button>
											</Col>
											<Col>
												<Button 
													onClick={() => setShowResultSection(false)}
													size="small"
												>
													Hide Result Section
												</Button>
											</Col>
										</Row>
									</>
								)}
							</Card>
						)}
					</>
				)}
			</Form>
		</Modal>
	);
};

export default ForwardConfigModal;
