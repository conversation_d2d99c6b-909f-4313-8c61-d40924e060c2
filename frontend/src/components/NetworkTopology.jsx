import React, { useEffect, useRef, useState } from "react";
import {
  Card,
  Button,
  Space,
  Tooltip,
  Badge,
  Spin,
  Tree,
  Row,
  Col,
  theme as antdTheme,
  message,
  Popconfirm,
  Flex,
  Select,
  Breadcrumb,
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  ClusterOutlined,
  GroupOutlined,
  LaptopOutlined,
  Pi<PERSON><PERSON>enterOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import {
  useGetNetworkTopologyDataQuery,
  useCleanupGroupsMutation,
} from "../app/services/groupsApi";
import { TopologyImage } from "./topology/TopologyImage";

// Graph configuration constants
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [56, 56],
  FORCE_LAYOUT: {
    linkDistance: 250,
    nodeStrength: -50,
    edgeStrength: 0.1,
    collideStrength: 0.8,
    nodeSize: 30,
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
  TREE_LAYOUT: {
    type: "compactBox",
    direction: "LR", // Left to Right
    getId: (d) => d.id,
    getHeight: () => 50, // Match node height
    getWidth: () => 180, // Match node width
    getVGap: () => 10, // Increased vertical gap
    getHGap: () => 50, // Increased horizontal gap
    radial: false,
  },
};

// Custom node with text wrapping for TreeGraph
G6.registerNode(
  "tree-node-with-wrap",
  {
    draw(cfg, group) {
      const { label = "", style = {} } = cfg;
      const {
        width = 150,
        height = 40,
        fill = "#f0f0f0",
        stroke = "#d9d9d9",
        lineWidth = 2,
        radius = 8,
      } = style;

      // Create the main rectangle
      const rect = group.addShape("rect", {
        attrs: {
          x: -width / 2,
          y: -height / 2,
          width,
          height,
          fill,
          stroke,
          lineWidth,
          radius,
        },
        name: "main-rect",
      });

      // Helper function to wrap text
      const wrapText = (text, maxWidth, fontSize = 10) => {
        // First, split by explicit newlines (\n)
        const textLines = text.split("\n");
        const lines = [];

        // Approximate character width (this is a rough estimate)
        const charWidth = fontSize * 0.6;
        const maxCharsPerLine = Math.floor(maxWidth / charWidth);

        textLines.forEach((line) => {
          // If the line is empty (from consecutive \n), add empty line
          if (line.trim() === "") {
            lines.push("");
            return;
          }

          // Split each line by spaces for word wrapping
          const words = line.split(/\s+/);
          let currentLine = "";

          words.forEach((word) => {
            // If a single word is too long, break it
            if (word.length > maxCharsPerLine) {
              if (currentLine) {
                lines.push(currentLine.trim());
                currentLine = "";
              }
              // Break long word into chunks
              for (let i = 0; i < word.length; i += maxCharsPerLine - 1) {
                const chunk = word.slice(i, i + maxCharsPerLine - 1);
                if (i + maxCharsPerLine - 1 < word.length) {
                  lines.push(chunk + "-");
                } else {
                  lines.push(chunk);
                }
              }
            } else {
              const testLine = currentLine + (currentLine ? " " : "") + word;
              if (testLine.length <= maxCharsPerLine) {
                currentLine = testLine;
              } else {
                if (currentLine) {
                  lines.push(currentLine.trim());
                }
                currentLine = word;
              }
            }
          });

          if (currentLine) {
            lines.push(currentLine.trim());
          }
        });

        return lines;
      };

      // Process the label text
      const fontSize = cfg.labelCfg?.style?.fontSize || 10;
      const textColor = cfg.labelCfg?.style?.fill || "#000";
      const fontWeight = cfg.labelCfg?.style?.fontWeight || "normal";

      // Reserve some padding for the text
      const textMaxWidth = width - 16; // 8px padding on each side
      const lines = wrapText(label, textMaxWidth, fontSize);

      // Limit to maximum 3 lines to prevent overflow
      const maxLines = 3;
      const displayLines = lines.slice(0, maxLines);
      if (lines.length > maxLines) {
        // Add ellipsis to the last line
        displayLines[maxLines - 1] =
          displayLines[maxLines - 1].slice(0, -3) + "...";
      }

      // Calculate text positioning
      const lineHeight = fontSize + 2;
      const totalTextHeight = displayLines.length * lineHeight;
      const startY = -totalTextHeight / 2 + fontSize / 2;

      // Add text lines
      displayLines.forEach((line, index) => {
        group.addShape("text", {
          attrs: {
            x: 0,
            y: startY + index * lineHeight,
            text: line,
            fontSize,
            fill: textColor,
            fontWeight,
            textAlign: "center",
            textBaseline: "middle",
          },
          name: `text-line-${index}`,
        });
      });

      return rect;
    },
    update(cfg, item) {
      // Get the group and redraw
      const group = item.getContainer();
      group.clear();
      this.draw(cfg, group);
    },
  },
  "rect"
);

// G6 Edge Registration
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle",
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

const NetworkTopology = () => {
  const { token } = antdTheme.useToken();
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedSubnetGroup, setSelectedSubnetGroup] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [currentGraphType, setCurrentGraphType] = useState("device"); // Always device graph now
  const [selectedNode, setSelectedNode] = useState(null);

  // Device grouping state
  const [groupingMode, setGroupingMode] = useState("modelname"); // "modelname", "macprefix", "ipprefix"
  const [currentViewLevel, setCurrentViewLevel] = useState("subnetgroup"); // "subnetgroup", "devicegroups", "devices"
  const [selectedDeviceGroup, setSelectedDeviceGroup] = useState(null);
  const [deviceGroups, setDeviceGroups] = useState({});
  const [breadcrumbs, setBreadcrumbs] = useState([]);

  // Persist grouping mode across sessions
  useEffect(() => {
    try {
      const saved = localStorage.getItem("nt_groupingMode");
      if (saved && ["modelname", "macprefix", "ipprefix"].includes(saved)) {
        setGroupingMode(saved);
      }
    } catch (e) {
      console.warn("Unable to read grouping mode from localStorage", e);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    try {
      localStorage.setItem("nt_groupingMode", groupingMode);
    } catch (e) {
      console.warn("Unable to persist grouping mode", e);
    }
  }, [groupingMode]);

  const {
    data: networkData,
    isLoading,
    refetch,
  } = useGetNetworkTopologyDataQuery();

  const [cleanupGroups] = useCleanupGroupsMutation();

  console.log("Network data:", networkData);

  // Extract data from the combined response
  const groups = networkData?.groups || [];
  const topologyData = networkData?.topologyData || {};
  const subnetGroups = networkData?.subnetGroups || [];

  // Debug subnet groups data
  console.log("Subnet groups:", subnetGroups);
  console.log("Number of subnet groups:", subnetGroups.length);

  // Using real data from API

  // Use real groups data
  const finalGroups = groups;

  // Use real data since it's available
  const finalSubnetGroups = subnetGroups;

  console.log("Final subnet groups being used:", finalSubnetGroups);
  console.log("Groups data:", groups);

  // Convert groups data to simple subnet group list for left panel
  const convertToTreeData = (groupsData) => {
    if (!groupsData || groupsData.length === 0) return [];

    const treeData = [];

    groupsData.forEach((relm) => {
      relm.regions?.forEach((region) => {
        region.zones?.forEach((zone) => {
          zone.subnets?.forEach((subnet) => {
            subnet.subnet_groups?.forEach((subnetGroup) => {
              const deviceCount = subnetGroup.devices?.length || 0;
              const subnetGroupNode = {
                title: (
                  <span>
                    <GroupOutlined
                      style={{ marginRight: 8, color: "#722ed1" }}
                    />
                    {subnetGroup.name}
                    <Badge count={deviceCount} style={{ marginLeft: 8 }} />
                  </span>
                ),
                key: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                type: "subnetGroup",
                data: subnetGroup,
                isLeaf: true,
              };

              treeData.push(subnetGroupNode);
            });
          });
        });
      });
    });

    return treeData;
  };

  const getDeviceDataByMac = (macAddress) => {
    for (const item of subnetGroups) {
      if (item.deviceData && item.deviceData.hasOwnProperty(macAddress)) {
        return item.deviceData[macAddress];
      }
    }
    return null;
  };

  // Device grouping utility functions
  const groupDevicesByModelName = (devices, deviceData) => {
    const groups = {};

    devices.forEach((deviceMac) => {
      const device = deviceData[deviceMac];
      const modelName = device?.modelname || device?.ModelName || "Unknown";

      if (!groups[modelName]) {
        groups[modelName] = {
          name: modelName,
          devices: [],
          count: 0,
        };
      }

      groups[modelName].devices.push(deviceMac);
      groups[modelName].count++;
    });

    return groups;
  };

  const groupDevicesByMacPrefix = (devices, deviceData) => {
    const groups = {};

    devices.forEach((deviceMac) => {
      // Extract first 3 bytes of MAC address (e.g., "00-60-E9" from "00-60-E9-12-34-56")
      const macPrefix = deviceMac.split("-").slice(0, 3).join("-");

      if (!groups[macPrefix]) {
        groups[macPrefix] = {
          name: `MAC: ${macPrefix}-XX-XX-XX`,
          devices: [],
          count: 0,
        };
      }

      groups[macPrefix].devices.push(deviceMac);
      groups[macPrefix].count++;
    });

    return groups;
  };

  const groupDevicesByIpPrefix = (devices, deviceData) => {
    const groups = {};

    devices.forEach((deviceMac) => {
      const device = deviceData[deviceMac];
      const ipAddress = device?.ipAddress || device?.IpAddress || "Unknown";

      // Extract first 3 octets of IP address (e.g., "192.168.31" from "**************")
      let ipPrefix = "Unknown";
      if (ipAddress !== "Unknown" && ipAddress.includes(".")) {
        const parts = ipAddress.split(".");
        if (parts.length >= 3) {
          ipPrefix = parts.slice(0, 3).join(".");
        }
      }

      if (!groups[ipPrefix]) {
        groups[ipPrefix] = {
          name: ipPrefix === "Unknown" ? "Unknown IP" : `IP: ${ipPrefix}.X`,
          devices: [],
          count: 0,
        };
      }

      groups[ipPrefix].devices.push(deviceMac);
      groups[ipPrefix].count++;
    });

    return groups;
  };

  const getDeviceGroups = (devices, deviceData, groupingMode) => {
    console.log("getDeviceGroups called with:");
    console.log("- devices:", devices);
    console.log(
      "- deviceData keys:",
      deviceData ? Object.keys(deviceData) : "null"
    );
    console.log("- groupingMode:", groupingMode);

    if (!devices || !Array.isArray(devices) || devices.length === 0) {
      console.log("No devices provided for grouping");
      return {};
    }

    if (!deviceData || typeof deviceData !== "object") {
      console.log("No deviceData provided for grouping");
      return {};
    }

    let result;
    switch (groupingMode) {
      case "macprefix":
        result = groupDevicesByMacPrefix(devices, deviceData);
        break;
      case "ipprefix":
        result = groupDevicesByIpPrefix(devices, deviceData);
        break;
      case "modelname":
      default:
        result = groupDevicesByModelName(devices, deviceData);
        break;
    }

    console.log("✅ Generated groups:", result);
    console.log("✅ Number of groups created:", Object.keys(result).length);
    return result;
  };

  // Convert hierarchical data to tree graph format
  const convertToTreeGraph = (nodeData, nodeType) => {
    if (!nodeData) return null;

    console.log("Converting to tree graph with node data:", nodeData);

    const createTreeNode = (data, type, level = 0) => {
      const getNodeStyle = (nodeType, level) => {
        const colors = {
          relm: "#188fff52",
          region: "#52c41a52",
          zone: "#faad1452",
          subnet: "#f5222d52",
          subnetGroup: "#722ed152",
          device: token.colorPrimaryBg,
        };

        return {
          fill: colors[nodeType] || "#d9d9d9",
          stroke: colors[nodeType] || "#d9d9d9",
          lineWidth: 2,
          radius: 8,
        };
      };

      const getIcon = (type) => {
        const icons = {
          relm: "🏢",
          region: "🌍",
          zone: "📍",
          subnet: "🔗",
          subnetGroup: "👥",
          device: "💻",
        };
        return icons[type] || "📁";
      };

      const node = {
        id: data.name || `${type}-${Math.random()}`,
        //collapsed: type === "subnetGroup",
        label:
          type === "subnetGroup"
            ? `${getIcon(type)} ${data.name}\n${
                data.devices?.length || 0
              } devices`
            : type === "device"
            ? `${getIcon(type)} ${data}\n${
                getDeviceDataByMac(data)?.ipAddress || "Unknown"
              }\n${getDeviceDataByMac(data)?.modelname || "Unknown"}`
            : `${getIcon(type)} ${data.name}`,
        type: "tree-node-with-wrap", // Use our custom node type
        style: {
          ...getNodeStyle(type, level),
          width: 180, // Slightly wider to accommodate wrapped text
          height: 50, // Taller to accommodate multiple lines
        },
        labelCfg: {
          style: {
            fill: token.colorText,
            fontSize: 10,
            fontWeight: "bold",
          },
        },
        children: [],
        // Add tooltip data for G6 tree graph nodes
        tooltipData: type === "device" ? data : data,
        nodeType: type,
      };
      return node;
    };

    // Build tree structure based on node type
    const buildTreeFromNode = (data, type) => {
      const rootNode = createTreeNode(data, type);

      if (type === "relm" && data.regions) {
        rootNode.children = data.regions.map((region) => {
          const regionNode = createTreeNode(region, "region", 1);
          if (region.zones) {
            regionNode.children = region.zones.map((zone) => {
              const zoneNode = createTreeNode(zone, "zone", 2);
              if (zone.subnets) {
                zoneNode.children = zone.subnets.map((subnet) => {
                  const subnetNode = createTreeNode(subnet, "subnet", 3);
                  if (subnet.subnet_groups) {
                    subnetNode.children = subnet.subnet_groups.map((sg) => {
                      const subnetGroupNode = createTreeNode(
                        sg,
                        "subnetGroup",
                        4
                      );
                      if (sg.devices) {
                        subnetGroupNode.children = sg.devices.map((device) =>
                          createTreeNode(device, "device", 5)
                        );
                      }
                      return subnetGroupNode;
                    });
                  }
                  return subnetNode;
                });
              }
              return zoneNode;
            });
          }
          return regionNode;
        });
      } else if (type === "region" && data.zones) {
        rootNode.children = data.zones.map((zone) => {
          const zoneNode = createTreeNode(zone, "zone", 1);
          if (zone.subnets) {
            zoneNode.children = zone.subnets.map((subnet) => {
              const subnetNode = createTreeNode(subnet, "subnet", 2);
              if (subnet.subnet_groups) {
                subnetNode.children = subnet.subnet_groups.map((sg) => {
                  const subnetGroupNode = createTreeNode(sg, "subnetGroup", 3);
                  if (sg.devices) {
                    subnetGroupNode.children = sg.devices.map((device) =>
                      createTreeNode(device, "device", 4)
                    );
                  }
                  return subnetGroupNode;
                });
              }
              return subnetNode;
            });
          }
          return zoneNode;
        });
      } else if (type === "zone" && data.subnets) {
        rootNode.children = data.subnets.map((subnet) => {
          const subnetNode = createTreeNode(subnet, "subnet", 1);
          if (subnet.subnet_groups) {
            subnetNode.children = subnet.subnet_groups.map((sg) => {
              const subnetGroupNode = createTreeNode(sg, "subnetGroup", 2);
              if (sg.devices) {
                subnetGroupNode.children = sg.devices.map((device) =>
                  createTreeNode(device, "device", 3)
                );
              }
              return subnetGroupNode;
            });
          }
          return subnetNode;
        });
      } else if (type === "subnet" && data.subnet_groups) {
        rootNode.children = data.subnet_groups.map((sg) => {
          const subnetGroupNode = createTreeNode(sg, "subnetGroup", 1);
          if (sg.devices) {
            subnetGroupNode.children = sg.devices.map((device) =>
              createTreeNode(device, "device", 2)
            );
          }
          return subnetGroupNode;
        });
      }

      return rootNode;
    };

    return buildTreeFromNode(nodeData, nodeType);
  };

  // Convert topology data to G6 graph format for right panel
  const convertToTopologyGraph = (selectedDevices, topologyData) => {
    const nodes = [];
    const edges = [];

    if (!selectedDevices || selectedDevices.length === 0) {
      return { nodes, edges };
    }

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    // Create nodes for selected devices
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};

      nodes.push({
        id: deviceMac,
        type: "image",
        img: TopologyImage(
          deviceTopologyData.modelname || deviceTopologyData.ModelName
        ),
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        labelCfg: {
          ...labelStyle,
          position: "bottom",
        },
        originalData: {
          mac: deviceMac,
          ipAddress:
            deviceTopologyData.ipAddress || deviceTopologyData.IpAddress,
          modelName:
            deviceTopologyData.modelname || deviceTopologyData.ModelName,
          services: deviceTopologyData.services || deviceTopologyData.Services,
          links:
            deviceTopologyData.linkData || deviceTopologyData.LinkData || [],
        },
      });
    });

    // Create edges between selected devices with duplicate handling
    const edgeMap = new Map(); // To track unique edges and prioritize blocked ports

    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      const linkData =
        deviceTopologyData.linkData || deviceTopologyData.LinkData || [];

      if (linkData && linkData.length > 0) {
        linkData.forEach((link) => {
          const source = link.source || link.Source;
          const target = link.target || link.Target;
          const sourcePort = link.sourcePort || link.SourcePort;
          const targetPort = link.targetPort || link.TargetPort;
          const linkType = link.linkType || link.LinkType;
          const blockedPort = link.blockedPort || link.BlockedPort;

          if (
            source &&
            target &&
            source !== target &&
            selectedDevices.includes(target)
          ) {
            // Create unique edge key (bidirectional)
            const edgeKey =
              source < target ? `${source}-${target}` : `${target}-${source}`;

            const edgeData = {
              id: `${source}-${target}`,
              source: source,
              target: target,
              label: `${source}_${sourcePort}\n${target}_${targetPort}`,
              type: "circle-running",
              color:
                blockedPort === "true"
                  ? "#faad14"
                  : linkType === "manual"
                  ? "#722ed1"
                  : token.colorTextDisabled,
              circleColor:
                blockedPort === "true" ||
                linkType === "dashed" ||
                linkType === "manual"
                  ? "transparent"
                  : token.colorPrimary,
              labelCfg: labelStyle,
              style: {
                lineWidth: 2,
                lineDash:
                  linkType === "dashed" || linkType === "manual"
                    ? [4, 4]
                    : undefined,
              },
              originalData: {
                sourcePort: sourcePort,
                targetPort: targetPort,
                linkType: linkType,
                blockedPort: blockedPort,
              },
              priority: blockedPort === "true" ? 1 : 0, // Higher priority for blocked ports
            };

            // Check if edge already exists
            if (edgeMap.has(edgeKey)) {
              const existingEdge = edgeMap.get(edgeKey);
              // Replace if current edge has higher priority (blocked port)
              if (edgeData.priority > existingEdge.priority) {
                edgeMap.set(edgeKey, edgeData);
              }
            } else {
              edgeMap.set(edgeKey, edgeData);
            }
          }
        });
      }
    });

    // Convert map to array and remove priority field
    edgeMap.forEach((edgeData) => {
      delete edgeData.priority;
      edges.push(edgeData);
    });

    return { nodes, edges };
  };

  // Handle subnet group selection - simplified to only handle subnet groups
  const handleTreeSelect = (selectedKeys, info) => {
    setSelectedKeys(selectedKeys);

    console.log("=== SUBNET GROUP SELECTION DEBUG ===");
    console.log("Selected node:", info.node);
    console.log("Node type:", info.node?.type);
    console.log("Node data:", info.node?.data);
    console.log("Node key:", info.node?.key);
    console.log("Current graph available:", !!graph);

    if (info.node && info.node.type === "subnetGroup") {
      // SubnetGroup selected - show device groups
      const subnetGroupData = info.node.data;
      setSelectedNode(subnetGroupData);

      // Find the corresponding subnet group topology data from the API response
      const keyParts = info.node.key.split("-");
      const subnetGroupTopology = finalSubnetGroups.find(
        (sg) =>
          sg.name === subnetGroupData.name &&
          sg.relmName === keyParts[1] &&
          sg.regionName === keyParts[2] &&
          sg.zoneName === keyParts[3] &&
          sg.subnetName === keyParts[4]
      );

      // Always prefer the topology data from API if available
      if (subnetGroupTopology) {
        console.log("Using subnet group topology data from API");
        setSelectedSubnetGroup(subnetGroupTopology);

        // Reset to subnet group level and clear device group selection
        setCurrentViewLevel("subnetgroup");
        setSelectedDeviceGroup(null);

        // Set up breadcrumbs
        setBreadcrumbs([
          {
            title: subnetGroupTopology.name,
            onClick: () => {
              setCurrentViewLevel("subnetgroup");
              setSelectedDeviceGroup(null);
              setBreadcrumbs([]);
            },
          },
        ]);

        // Generate device groups based on current grouping mode
        if (subnetGroupTopology.devices && subnetGroupTopology.deviceData) {
          console.log("=== DEVICE GROUPING DEBUG ===");
          console.log("Subnet Group:", subnetGroupTopology.name);
          console.log("Devices:", subnetGroupTopology.devices);
          console.log("Device Data:", subnetGroupTopology.deviceData);
          console.log("Grouping Mode:", groupingMode);

          const groups = getDeviceGroups(
            subnetGroupTopology.devices,
            subnetGroupTopology.deviceData,
            groupingMode
          );

          console.log("🔄 Setting device groups in state:", groups);
          setDeviceGroups(groups);

          // Log for debugging
          console.log("Generated device groups:", groups);
          console.log("Number of groups:", Object.keys(groups).length);
          console.log("Device groups will be displayed by useEffect");
          console.log("=== END DEVICE GROUPING DEBUG ===");
        } else {
          console.log(
            "No devices or deviceData available for grouping in topology data"
          );
          setDeviceGroups({});
        }
      } else {
        console.log("No topology data found, using basic subnet group data");
        setSelectedSubnetGroup(subnetGroupData);

        // Reset to subnet group level and clear device group selection
        setCurrentViewLevel("subnetgroup");
        setSelectedDeviceGroup(null);
        setBreadcrumbs([
          {
            title: subnetGroupData.name,
            onClick: () => {
              setCurrentViewLevel("subnetgroup");
              setSelectedDeviceGroup(null);
              setBreadcrumbs([]);
            },
          },
        ]);

        // Generate device groups using global topologyData as deviceData map
        if (subnetGroupData.devices && subnetGroupData.devices.length > 0) {
          const groups = getDeviceGroups(
            subnetGroupData.devices,
            topologyData,
            groupingMode
          );
          setDeviceGroups(groups);
        } else {
          setDeviceGroups({});
        }
      }
    } else {
      // Clear selection if not a subnet group
      console.log("No subnet group selected, clearing state");
      setSelectedSubnetGroup(null);
      setSelectedNode(null);
      setCurrentViewLevel("subnetgroup");
      setSelectedDeviceGroup(null);
      setDeviceGroups({});
      setBreadcrumbs([]);

      // Clear the graph
      if (graph && !(graph instanceof G6.TreeGraph)) {
        updateTopologyGraph([]);
      }
    }
    console.log("=== END SUBNET GROUP SELECTION DEBUG ===");
  };

  // Auto-expand all tree nodes
  useEffect(() => {
    if (finalGroups && finalGroups.length > 0) {
      const treeData = convertToTreeData(finalGroups);
      const allKeys = [];

      const extractKeys = (nodes) => {
        nodes.forEach((node) => {
          allKeys.push(node.key);
          if (node.children && node.children.length > 0) {
            extractKeys(node.children);
          }
        });
      };

      extractKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [finalGroups]);

  // Re-generate device groups when grouping mode changes
  useEffect(() => {
    if (
      selectedSubnetGroup &&
      selectedSubnetGroup.devices &&
      selectedSubnetGroup.deviceData
    ) {
      console.log("Grouping mode changed to:", groupingMode);
      const groups = getDeviceGroups(
        selectedSubnetGroup.devices,
        selectedSubnetGroup.deviceData,
        groupingMode
      );
      setDeviceGroups(groups);
      console.log("Updated device groups:", groups);

      // Update the graph to show device groups
      if (currentViewLevel === "subnetgroup") {
        updateTopologyGraphWithDeviceGroups(groups);
      }
    }
  }, [groupingMode, selectedSubnetGroup]);

  // Clean container before initializing new graph
  const cleanContainer = () => {
    if (containerRef.current) {
      try {
        // Use innerHTML to clear all content safely
        containerRef.current.innerHTML = "";
        console.log("Container cleaned successfully");
      } catch (error) {
        console.error("Error cleaning container:", error);
        // Fallback: try to remove children one by one
        try {
          const children = Array.from(containerRef.current.children);
          children.forEach((child) => {
            if (containerRef.current.contains(child)) {
              containerRef.current.removeChild(child);
            }
          });
        } catch (fallbackError) {
          console.error("Fallback cleanup also failed:", fallbackError);
        }
      }
    }
  };

  // Safely remove any leftover tooltips (useful during hot-reload/unmount)
  const cleanupTooltips = () => {
    try {
      const nodes = document.querySelectorAll(
        "#g6-device-tooltip, #g6-tooltip"
      );
      nodes.forEach((el) => {
        try {
          if (el && el.parentNode) {
            el.parentNode.removeChild(el);
          }
        } catch (e) {
          console.warn("Tooltip node cleanup skipped:", e);
        }
      });
    } catch (e) {
      console.warn("Error during tooltip cleanup:", e);
    }
  };

  // Initialize device topology graph
  const initDeviceGraph = () => {
    if (!containerRef.current) {
      console.error("Container ref not available for graph initialization");
      return null;
    }

    try {
      const width = containerRef.current.offsetWidth || 800;
      const height = containerRef.current.offsetHeight || 400;

      console.log(
        `Initializing device graph in container with dimensions: ${width}x${height}`
      );

      const newGraph = new G6.Graph({
        container: "network-topology-container",
        width,
        height,
        renderer: "svg",
        linkCenter: true,
        animate: true,
        modes: {
          default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
        },
        defaultNode: {
          type: "image",
          size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        },
        defaultEdge: {
          type: "circle-running",
        },
        layout: {
          type: "force",
          ...GRAPH_CONFIG.FORCE_LAYOUT,
        },
      });

      // Add tooltip functionality to device graph nodes
      newGraph.on("node:mouseenter", (e) => {
        const { item } = e;
        const model = item.getModel();

        if (model.originalData) {
          // Create tooltip element
          const tooltip = document.createElement("div");
          tooltip.id = "g6-device-tooltip";
          tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          `;

          // Create device tooltip content
          const deviceData = model.originalData;
          tooltip.innerHTML = `
          ${
            model.id && deviceData.mac !== model.id
              ? `<div><strong>Name:</strong> ${model.id}</div>`
              : ""
          }
          ${
            deviceData.mac
              ? `<div><strong>MAC:</strong> ${deviceData.mac}</div>`
              : ""
          }
          ${
            deviceData.ipAddress || deviceData.IpAddress
              ? `<div><strong>IP Address:</strong> ${
                  deviceData.ipAddress || deviceData.IpAddress
                }</div>`
              : ""
          }
            ${
              deviceData.modelname || deviceData.ModelName
                ? `<div><strong>Model:</strong> ${
                    deviceData.modelname || deviceData.ModelName
                  }</div>`
                : ""
            }
            
            ${
              deviceData.services || deviceData.Services
                ? `<div><strong>Services:</strong> ${
                    deviceData.services || deviceData.Services
                  }</div>`
                : ""
            }
          `;

          document.body.appendChild(tooltip);

          // Position tooltip
          const updateTooltipPosition = (event) => {
            tooltip.style.left = event.clientX + 10 + "px";
            tooltip.style.top = event.clientY - 10 + "px";
          };

          updateTooltipPosition(e.originalEvent || e);

          // Store tooltip reference for cleanup
          item._tooltip = tooltip;
          item._tooltipHandler = updateTooltipPosition;

          // Add mouse move listener to update tooltip position
          document.addEventListener("mousemove", updateTooltipPosition);
        }
      });

      newGraph.on("node:mouseleave", (e) => {
        const { item } = e;
        if (item._tooltip) {
          try {
            if (item._tooltip.parentNode === document.body) {
              document.body.removeChild(item._tooltip);
            }
          } catch (e) {
            console.warn("Tooltip removeChild skipped:", e);
          }
          try {
            document.removeEventListener("mousemove", item._tooltipHandler);
          } catch (_) {}
          item._tooltip = null;
          item._tooltipHandler = null;
        }
      });

      setGraph(newGraph);
      return newGraph;
    } catch (error) {
      console.error("Error initializing device graph:", error);
      return null;
    }
  };

  // Initialize tree graph
  const initTreeGraph = () => {
    if (!containerRef.current) {
      console.error(
        "Container ref not available for tree graph initialization"
      );
      return null;
    }

    try {
      const width = containerRef.current.offsetWidth || 800;
      const height = containerRef.current.offsetHeight || 400;

      console.log(
        `Initializing tree graph in container with dimensions: ${width}x${height}`
      );

      const newGraph = new G6.TreeGraph({
        container: "network-topology-container",
        width,
        height,
        renderer: "svg",
        animate: true,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                console.log("Tree node collapsed:", item, collapsed);
                console.log("Tree node collapsed1:", item.getModel());
                const data = item.getModel();
                data.collapsed = collapsed;
                return true;
              },
            },
            "drag-canvas",
            "zoom-canvas",
          ],
        },
        defaultNode: {
          type: "tree-node-with-wrap", // Use our custom node type
          size: [180, 50], // Match the updated dimensions
          anchorPoints: [
            [0, 0.5],
            [1, 0.5],
          ],
          labelCfg: {
            style: {
              fill: "#000",
              fontSize: 10,
              fontWeight: "bold",
            },
          },
        },
        defaultEdge: {
          type: "cubic-horizontal",
          style: {
            stroke: "#A3B1BF",
            lineWidth: 2,
          },
        },
        layout: GRAPH_CONFIG.TREE_LAYOUT,
      });

      // Add tooltip functionality to tree graph nodes
      newGraph.on("node:mouseenter", (e) => {
        const { item } = e;
        const model = item.getModel();

        if (model.tooltipData) {
          // Create tooltip element
          const tooltip = document.createElement("div");
          tooltip.id = "g6-tooltip";
          tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          `;

          // Create tooltip content based on node type
          const data = model.tooltipData;
          const type = model.nodeType;

          let tooltipContent = "";

          if (type === "device") {
            const deviceData = getDeviceDataByMac(data);
            if (deviceData) {
              tooltipContent = `
                <div><strong>MAC:</strong> ${data}</div>
                <div><strong>IP Address:</strong> ${
                  deviceData.ipAddress || deviceData.IpAddress || "N/A"
                }</div>
                <div><strong>Model:</strong> ${
                  deviceData.modelname || deviceData.ModelName || "Unknown"
                }</div>
                ${
                  deviceData.hostname || deviceData.Hostname
                    ? `<div><strong>Hostname:</strong> ${
                        deviceData.hostname || deviceData.Hostname
                      }</div>`
                    : ""
                }
                ${
                  deviceData.services ||
                  deviceData.Services ||
                  deviceData.scannedby ||
                  deviceData.ScannedBy
                    ? `<div><strong>Services:</strong> ${
                        deviceData.services ||
                        deviceData.Services ||
                        deviceData.scannedby ||
                        deviceData.ScannedBy
                      }</div>`
                    : ""
                }
              `;
            } else {
              tooltipContent = `
                <div><strong>MAC:</strong> ${data}</div>
                <div><strong>Type:</strong> Device</div>
              `;
            }
          } else {
            // For hierarchical nodes (relm, region, zone, subnet, subnetGroup)
            tooltipContent = `
              <div><strong>Name:</strong> ${data.name}</div>
              <div><strong>Type:</strong> ${
                type.charAt(0).toUpperCase() + type.slice(1)
              }</div>
              ${
                data.comment
                  ? `<div><strong>Comment:</strong> ${data.comment}</div>`
                  : ""
              }
            `;

            // Add specific counts based on type
            if (type === "relm" && data.regions) {
              tooltipContent += `<div><strong>Location:</strong> ${
                data.location || "N/A"
              }</div>`;
              tooltipContent += `<div><strong>Regions:</strong> ${data.regions.length}</div>`;
            } else if (type === "region" && data.zones) {
              tooltipContent += `<div><strong>Zones:</strong> ${data.zones.length}</div>`;
            } else if (type === "zone" && data.subnets) {
              tooltipContent += `<div><strong>Subnets:</strong> ${data.subnets.length}</div>`;
            } else if (type === "subnet" && data.subnet_groups) {
              tooltipContent += `<div><strong>Subnet Groups:</strong> ${data.subnet_groups.length}</div>`;
            } else if (type === "subnetGroup" && data.devices) {
              tooltipContent += `<div><strong>Devices:</strong> ${data.devices.length}</div>`;
            }

            // Add timestamps if available
            if (data.created_at) {
              try {
                const createdDate = new Date(data.created_at).toLocaleString();
                tooltipContent += `<div><strong>Created:</strong> ${createdDate}</div>`;
              } catch {
                tooltipContent += `<div><strong>Created:</strong> ${data.created_at}</div>`;
              }
            }
            if (data.updated_at) {
              try {
                const updatedDate = new Date(data.updated_at).toLocaleString();
                tooltipContent += `<div><strong>Updated:</strong> ${updatedDate}</div>`;
              } catch {
                tooltipContent += `<div><strong>Updated:</strong> ${data.updated_at}</div>`;
              }
            }
          }

          tooltip.innerHTML = tooltipContent;
          document.body.appendChild(tooltip);

          // Position tooltip
          const updateTooltipPosition = (event) => {
            tooltip.style.left = event.clientX + 10 + "px";
            tooltip.style.top = event.clientY - 10 + "px";
          };

          updateTooltipPosition(e.originalEvent || e);

          // Store tooltip reference for cleanup
          item._tooltip = tooltip;
          item._tooltipHandler = updateTooltipPosition;

          // Add mouse move listener to update tooltip position
          document.addEventListener("mousemove", updateTooltipPosition);
        }
      });

      newGraph.on("node:mouseleave", (e) => {
        const { item } = e;
        if (item._tooltip) {
          try {
            if (item._tooltip.parentNode === document.body) {
              document.body.removeChild(item._tooltip);
            }
          } catch (e) {
            console.warn("Tooltip removeChild skipped:", e);
          }
          try {
            document.removeEventListener("mousemove", item._tooltipHandler);
          } catch (_) {}
          item._tooltip = null;
          item._tooltipHandler = null;
        }
      });

      setGraph(newGraph);
      return newGraph;
    } catch (error) {
      console.error("Error initializing tree graph:", error);
      return null;
    }
  };

  // Initialize appropriate graph based on type
  const initGraph = (graphType = "device") => {
    if (graphType === "tree") {
      return initTreeGraph();
    } else {
      return initDeviceGraph();
    }
  };

  // Retry helper to wait for container to be ready during fast refresh / re-renders
  const initGraphWithRetry = (graphType = "device", onReady, attempt = 0) => {
    const el = containerRef.current;
    const domEl =
      typeof document !== "undefined"
        ? document.getElementById("network-topology-container")
        : null;

    if (!el || !domEl) {
      if (attempt < 10) {
        requestAnimationFrame(() =>
          initGraphWithRetry(graphType, onReady, attempt + 1)
        );
      } else {
        console.error("Container not ready after retries");
        setLoading(false);
      }
      return;
    }

    const g = initGraph(graphType);
    if (g) {
      try {
        onReady && onReady(g);
      } catch (e) {
        console.error("Error in onReady after initGraph:", e);
      }
    } else if (attempt < 5) {
      setTimeout(() => initGraphWithRetry(graphType, onReady, attempt + 1), 50);
    } else {
      console.error("Failed to initialize graph after retries");
      setLoading(false);
    }
  };

  // Validate graph data before rendering
  const validateGraphData = (graphData) => {
    if (!graphData || typeof graphData !== "object") {
      console.error("Invalid graph data: not an object");
      return false;
    }

    if (!Array.isArray(graphData.nodes)) {
      console.error("Invalid graph data: nodes is not an array");
      return false;
    }

    if (!Array.isArray(graphData.edges)) {
      console.error("Invalid graph data: edges is not an array");
      return false;
    }

    // Validate nodes
    for (let i = 0; i < graphData.nodes.length; i++) {
      const node = graphData.nodes[i];
      if (!node || typeof node !== "object") {
        console.error(`Invalid node at index ${i}: not an object`);
        return false;
      }
      if (!node.id || typeof node.id !== "string") {
        console.error(`Invalid node at index ${i}: missing or invalid id`);
        return false;
      }
    }

    // Validate edges
    for (let i = 0; i < graphData.edges.length; i++) {
      const edge = graphData.edges[i];
      if (!edge || typeof edge !== "object") {
        console.error(`Invalid edge at index ${i}: not an object`);
        return false;
      }
      if (!edge.id || typeof edge.id !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid id`);
        return false;
      }
      if (!edge.source || typeof edge.source !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid source`);
        return false;
      }
      if (!edge.target || typeof edge.target !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid target`);
        return false;
      }
    }

    return true;
  };

  // Update topology graph with selected devices
  const updateTopologyGraph = (selectedDevices) => {
    setLoading(true);
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    try {
      const graphData = convertToTopologyGraph(selectedDevices, topologyData);

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error("Graph data validation failed, skipping render");
        return;
      }

      if (graphData && graphData.nodes) {
        // Clear existing data first
        graph.clear();

        // Set new data with error handling
        try {
          graph.data(graphData);
          graph.render();
          graph.updateLayout();

          // Fit view and refresh animations after a short delay
          setTimeout(() => {
            try {
              graph.fitView();
              graph.getEdges().forEach((edge) => {
                try {
                  edge.setState("refresh", true);
                  edge.setState("refresh", false);
                } catch (edgeError) {
                  console.error("Error refreshing edge animation:", edgeError);
                }
              });
            } catch (fitViewError) {
              console.error("Error in fitView or edge refresh:", fitViewError);
            }
          }, 200);
          setLoading(false);
        } catch (renderError) {
          setLoading(false);
          console.error("Error rendering graph:", renderError);
          graph.clear();
        }
      }
      setLoading(false);
    } catch (error) {
      console.error("Error in updateTopologyGraph:", error);
    }
  };

  // Update tree graph with hierarchical data
  const updateTreeGraph = (nodeData, nodeType, targetGraph = graph) => {
    if (!targetGraph) {
      console.error("Graph not initialized");
      return;
    }

    // Only proceed if current graph is a TreeGraph
    if (!(targetGraph instanceof G6.TreeGraph)) {
      console.error("Current graph is not a TreeGraph");
      return;
    }

    try {
      const treeData = convertToTreeGraph(nodeData, nodeType);

      if (treeData) {
        // Clear existing data first
        targetGraph.clear();

        // Set new tree data
        try {
          targetGraph.data(treeData);
          targetGraph.render();
          targetGraph.fitView();
        } catch (renderError) {
          console.error("Error rendering tree graph:", renderError);
          targetGraph.clear();
        }
      } else {
        // Handle empty state
        targetGraph.clear();
        const emptyData = {
          id: "no-data",
          label: `No ${nodeType} data available`,
          type: "rect",
          style: {
            fill: "#f0f0f0",
            stroke: "#d9d9d9",
          },
        };

        try {
          targetGraph.data(emptyData);
          targetGraph.render();
          targetGraph.fitView();
        } catch (emptyStateError) {
          console.error("Error rendering empty tree state:", emptyStateError);
        }
      }
    } catch (error) {
      console.error("Error in updateTreeGraph:", error);
    }
  };

  // Update topology graph with pre-processed subnet group data
  const updateTopologyGraphWithSubnetGroup = (subnetGroupTopology) => {
    setLoading(true);
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    try {
      const nodes = [];
      const edges = [];

      const labelStyle = {
        style: {
          fill: token.colorText,
          fontSize: 12,
          background: {
            fill: "transparent",
            padding: [2, 2, 2, 2],
          },
        },
      };

      // Create nodes from pre-processed device data
      if (
        subnetGroupTopology.devices &&
        Array.isArray(subnetGroupTopology.devices)
      ) {
        subnetGroupTopology.devices.forEach((deviceMac) => {
          if (!deviceMac || typeof deviceMac !== "string") {
            console.warn("Invalid device MAC:", deviceMac);
            return;
          }

          const deviceData = subnetGroupTopology.deviceData?.[deviceMac] || {};

          nodes.push({
            id: deviceMac,
            label: `${
              deviceData.ipAddress || deviceData.IpAddress || "N/A"
            }\n${deviceMac}\n${
              deviceData.modelname || deviceData.ModelName || "Unknown"
            }`,
            type: "image",
            img: TopologyImage(deviceData.modelname || deviceData.ModelName),
            size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
            labelCfg: {
              ...labelStyle,
              position: "bottom",
            },
            originalData: deviceData,
          });
        });
      }

      // Create edges from pre-processed connections with duplicate handling
      const edgeMap = new Map(); // To track unique edges and prioritize blocked ports

      if (
        subnetGroupTopology.connections &&
        Array.isArray(subnetGroupTopology.connections)
      ) {
        subnetGroupTopology.connections.forEach((connection) => {
          if (!connection || typeof connection !== "object") {
            console.warn("Invalid connection:", connection);
            return;
          }

          if (!connection.source || !connection.target) {
            console.warn("Connection missing source or target:", connection);
            return;
          }

          // Create unique edge key (bidirectional)
          const edgeKey =
            connection.source < connection.target
              ? `${connection.source}-${connection.target}`
              : `${connection.target}-${connection.source}`;

          const edgeData = {
            id: `${connection.source}-${connection.target}`,
            source: connection.source,
            target: connection.target,
            label: `${connection.source}_${connection.sourcePort || "N/A"}\n${
              connection.target
            }_${connection.targetPort || "N/A"}`,
            type: "circle-running",
            color:
              connection.blockedPort === "true"
                ? "#faad14"
                : connection.linkType === "manual"
                ? "#722ed1"
                : token.colorTextDisabled,
            circleColor:
              connection.blockedPort === "true" ||
              connection.linkType === "dashed" ||
              connection.linkType === "manual"
                ? "transparent"
                : token.colorPrimary,
            labelCfg: labelStyle,
            style: {
              lineWidth: 2,
              lineDash:
                connection.linkType === "dashed" ||
                connection.linkType === "manual"
                  ? [4, 4]
                  : undefined,
            },
            originalData: connection,
            priority: connection.blockedPort === "true" ? 1 : 0, // Higher priority for blocked ports
          };

          // Check if edge already exists
          if (edgeMap.has(edgeKey)) {
            const existingEdge = edgeMap.get(edgeKey);
            // Replace if current edge has higher priority (blocked port)
            if (edgeData.priority > existingEdge.priority) {
              edgeMap.set(edgeKey, edgeData);
            }
          } else {
            edgeMap.set(edgeKey, edgeData);
          }
        });
      }

      // Convert map to array and remove priority field
      edgeMap.forEach((edgeData) => {
        delete edgeData.priority;
        edges.push(edgeData);
      });

      const graphData = { nodes, edges };

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error(
          "Subnet group graph data validation failed, skipping render"
        );
        return;
      }

      if (graphData && graphData.nodes && graphData.nodes.length > 0) {
        // Clear existing data first
        graph.clear();

        // Set new data with error handling
        try {
          graph.data(graphData);
          graph.render();
          graph.updateLayout();

          // Fit view and refresh animations after a short delay
          setTimeout(() => {
            try {
              graph.fitView();
              graph.getEdges().forEach((edge) => {
                try {
                  edge.setState("refresh", true);
                  edge.setState("refresh", false);
                } catch (edgeError) {
                  console.error("Error refreshing edge animation:", edgeError);
                }
              });
            } catch (fitViewError) {
              console.error("Error in fitView or edge refresh:", fitViewError);
            }
          }, 200);
          setLoading(false);
        } catch (renderError) {
          setLoading(false);
          console.error("Error rendering subnet group graph:", renderError);
          graph.clear();
        }
      } else if (graphData && graphData.nodes && graphData.nodes.length === 0) {
        // Handle empty state
        try {
          graph.clear();
          const emptyData = {
            nodes: [
              {
                id: "no-devices",
                label: "No devices in this SubnetGroup",
                type: "rect",
                style: {
                  fill: "#f0f0f0",
                  stroke: "#d9d9d9",
                },
              },
            ],
            edges: [],
          };

          if (validateGraphData(emptyData)) {
            graph.data(emptyData);
            graph.render();
            graph.fitView();
            setLoading(false);
          }
        } catch (emptyStateError) {
          console.error("Error rendering empty state:", emptyStateError);
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraphWithSubnetGroup:", error);
    }
  };

  // Update topology graph with device groups
  const updateTopologyGraphWithDeviceGroups = (deviceGroups) => {
    setLoading(true);
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    try {
      const nodes = [];
      const edges = [];

      const labelStyle = {
        style: {
          fill: token.colorText,
          fontSize: 12,
          background: {
            fill: "transparent",
            padding: [2, 2, 2, 2],
          },
        },
      };

      // Style by grouping mode
      const styleMap = {
        modelname: { fill: "#f9f0ff", stroke: "#722ed1" },
        macprefix: { fill: "#e6f7ff", stroke: "#1890ff" },
        ipprefix: { fill: "#f6ffed", stroke: "#52c41a" },
      };
      const icon =
        groupingMode === "modelname"
          ? "🧩"
          : groupingMode === "macprefix"
          ? "🔢"
          : "🌐";

      // Create nodes for device groups
      Object.keys(deviceGroups).forEach((groupKey) => {
        const group = deviceGroups[groupKey];

        nodes.push({
          id: `group-${groupKey}`,
          label: `${icon} ${group.name}\n${group.count} devices`,
          type: "rect",
          size: [180, 70],
          style: {
            fill: styleMap[groupingMode]?.fill || "#e6f7ff",
            stroke: styleMap[groupingMode]?.stroke || "#1890ff",
            lineWidth: 2,
            radius: 10,
            shadowColor: "rgba(0,0,0,0.15)",
            shadowBlur: 8,
          },
          labelCfg: {
            ...labelStyle,
            position: "center",
          },
          originalData: {
            groupKey: groupKey,
            groupData: group,
            isDeviceGroup: true,
          },
        });
      });

      const graphData = { nodes, edges };

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error(
          "Device groups graph data validation failed, skipping render"
        );
        return;
      }

      if (graphData && graphData.nodes && graphData.nodes.length > 0) {
        // Clear existing data first
        graph.clear();

        // Set new data with error handling
        try {
          graph.data(graphData);
          graph.render();
          graph.updateLayout();

          // Remove previous handler and add click handler for device groups
          try {
            graph.off("node:click");
          } catch (e) {}
          graph.on("node:click", (e) => {
            const { item } = e;
            const model = item.getModel();

            if (model.originalData && model.originalData.isDeviceGroup) {
              handleDeviceGroupClick(
                model.originalData.groupData,
                model.originalData.groupKey
              );
            }
          });

          // Fit view after a short delay
          setTimeout(() => {
            try {
              graph.fitView();
            } catch (fitViewError) {
              console.error("Error in fitView:", fitViewError);
            }
          }, 200);
          setLoading(false);
        } catch (renderError) {
          setLoading(false);
          console.error("Error rendering device groups graph:", renderError);
          graph.clear();
        }
      } else {
        // Handle empty state
        try {
          graph.clear();
          const emptyData = {
            nodes: [
              {
                id: "no-groups",
                label: "No device groups available",
                type: "rect",
                style: {
                  fill: "#f0f0f0",
                  stroke: "#d9d9d9",
                },
              },
            ],
            edges: [],
          };

          if (validateGraphData(emptyData)) {
            graph.data(emptyData);
            graph.render();
            graph.fitView();
            setLoading(false);
          }
        } catch (emptyStateError) {
          console.error(
            "Error rendering empty device groups state:",
            emptyStateError
          );
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraphWithDeviceGroups:", error);
    }
  };

  // Update topology graph with individual devices from a selected group
  const updateTopologyGraphWithDevices = (deviceMacs) => {
    if (!graph || !selectedSubnetGroup) {
      console.error("Graph not initialized or no subnet group selected");
      return;
    }

    try {
      // Use the existing subnet group topology update but with filtered devices
      if (selectedSubnetGroup.deviceData && selectedSubnetGroup.connections) {
        // Filter connections to only include selected devices
        const filteredConnections = selectedSubnetGroup.connections.filter(
          (conn) =>
            deviceMacs.includes(conn.source) && deviceMacs.includes(conn.target)
        );

        const filteredSubnetGroup = {
          ...selectedSubnetGroup,
          devices: deviceMacs,
          connections: filteredConnections,
        };

        updateTopologyGraphWithSubnetGroup(filteredSubnetGroup);
      } else {
        // Fallback to basic topology update
        updateTopologyGraph(deviceMacs);
      }
    } catch (error) {
      console.error("Error in updateTopologyGraphWithDevices:", error);
    }
  };

  // Handle device group click to drill down to individual devices
  const handleDeviceGroupClick = (groupData, groupKey) => {
    console.log(
      "Device group clicked:",
      groupData.name,
      "with",
      groupData.count,
      "devices"
    );

    setSelectedDeviceGroup(groupData);
    setCurrentViewLevel("devices");

    // Update breadcrumbs
    setBreadcrumbs([
      {
        title: selectedSubnetGroup.name,
        onClick: () => {
          setCurrentViewLevel("subnetgroup");
          setSelectedDeviceGroup(null);
          setBreadcrumbs([]);
        },
      },
      {
        title: groupData.name,
        onClick: () => {
          setCurrentViewLevel("devices");
          // Keep current state
        },
      },
    ]);
  };

  // Control handlers
  const handleZoomIn = () => {
    if (graph) {
      graph.zoomTo(graph.getZoom() * 1.2);
    }
  };

  const handleZoomOut = () => {
    if (graph) {
      graph.zoomTo(graph.getZoom() * 0.8);
    }
  };

  const handleFitView = () => {
    if (graph && containerRef.current) {
      console.log("Manual fitView triggered");

      // Update graph size to current container dimensions
      const width = containerRef.current.offsetWidth;
      const height = containerRef.current.offsetHeight;

      if (width > 0 && height > 0) {
        graph.changeSize(width, height);
        graph.render();
        graph.fitView();
      }
    }
  };
  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  const handleCleanupGroups = async () => {
    try {
      const result = await cleanupGroups().unwrap();
      if (result.hasChanges) {
        message.success(result.message);
      } else {
        message.info(result.message);
      }
    } catch (error) {
      message.error(`Error during cleanup: ${error.data || error.message}`);
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    if (containerRef.current && !graph && !isLoading && networkData) {
      try {
        // Always start with device graph initially
        const newGraph = initGraph("device");
        if (newGraph) {
          const graphData = convertToTopologyGraph([], topologyData);

          // Validate initial data
          if (validateGraphData(graphData)) {
            newGraph.data(graphData);
            newGraph.render();
            setLoading(false);
          } else {
            console.error("Initial graph data validation failed");
            setLoading(false);
          }
        } else {
          console.error("Failed to initialize graph");
          setLoading(false);
        }
      } catch (error) {
        console.error("Error during graph initialization:", error);
        setLoading(false);
      }
    }
  }, [graph, isLoading, networkData, topologyData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (graph) {
        try {
          graph.destroy();
        } catch (error) {
          console.error("Error destroying graph on unmount:", error);
        }
      }
      // Clean container on unmount with a small delay
      setTimeout(() => {
        cleanupTooltips();
        cleanContainer();
      }, 10);
    };
  }, []);

  // Handle container resize
  useEffect(() => {
    if (!containerRef.current || !graph) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;

        if (width > 0 && height > 0) {
          // Update graph dimensions
          graph.changeSize(width, height);
          graph.render();
          graph.fitView();
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [graph]);

  // Handle window resize as fallback
  useEffect(() => {
    if (!graph) return;

    const handleWindowResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        const height = containerRef.current.offsetHeight;

        if (width > 0 && height > 0) {
          graph.changeSize(width, height);
          graph.render();
          graph.fitView();
        }
      }
    };

    window.addEventListener("resize", handleWindowResize);

    return () => {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, [graph]);

  // Initialize device graph on mount and ensure it's available
  useEffect(() => {
    if (containerRef.current && !graph && !isLoading && networkData) {
      console.log("Initializing device graph...");
      try {
        const newGraph = initDeviceGraph();
        if (newGraph) {
          console.log("Device graph initialized successfully");
          setGraph(newGraph);
          setLoading(false);
        } else {
          console.error("Failed to initialize device graph");
          setLoading(false);
        }
      } catch (error) {
        console.error("Error during graph initialization:", error);
        setLoading(false);
      }
    }
  }, [networkData, isLoading]);

  // Ensure graph is available when subnet group is selected
  useEffect(() => {
    if (selectedSubnetGroup && !graph && containerRef.current && networkData) {
      console.log(
        "Subnet group selected but no graph available, initializing..."
      );
      try {
        const newGraph = initDeviceGraph();
        if (newGraph) {
          console.log("Device graph initialized for subnet group");
          setGraph(newGraph);
        } else {
          console.error("Failed to initialize device graph for subnet group");
        }
      } catch (error) {
        console.error("Error initializing graph for subnet group:", error);
      }
    }
  }, [selectedSubnetGroup, graph, networkData]);

  // Regenerate device groups when grouping mode changes
  useEffect(() => {
    if (selectedSubnetGroup && selectedSubnetGroup.devices) {
      const deviceDataMap = selectedSubnetGroup.deviceData || topologyData;
      if (deviceDataMap) {
        const groups = getDeviceGroups(
          selectedSubnetGroup.devices,
          deviceDataMap,
          groupingMode
        );
        setDeviceGroups(groups);
      } else {
        console.log("No deviceData available to regenerate groups");
        setDeviceGroups({});
      }
    }
  }, [groupingMode, selectedSubnetGroup, topologyData]);

  // Update topology when data changes
  useEffect(() => {
    console.log("=== DATA UPDATE USEEFFECT TRIGGERED ===");
    console.log("Graph available:", !!graph);
    console.log("Selected subnet group:", selectedSubnetGroup?.name);
    console.log("Graph type:", graph?.constructor?.name);

    if (graph && selectedSubnetGroup) {
      console.log(
        "✅ Updating device topology with subnet group:",
        selectedSubnetGroup.name
      );

      // Add a small delay to ensure graph switching is complete
      setLoading(true);
      setTimeout(() => {
        console.log("=== USEEFFECT GRAPH UPDATE DEBUG ===");
        console.log("Current view level:", currentViewLevel);
        console.log("Device groups:", deviceGroups);
        console.log("Device groups keys:", Object.keys(deviceGroups));
        console.log(
          "Number of device groups:",
          Object.keys(deviceGroups).length
        );
        console.log("Selected subnet group:", selectedSubnetGroup?.name);
        console.log("Selected device group:", selectedDeviceGroup?.name);

        if (currentViewLevel === "subnetgroup") {
          // Show device groups
          if (deviceGroups && Object.keys(deviceGroups).length > 0) {
            console.log("✅ CONDITION MET: Showing device groups in graph");
            console.log("Device groups to display:", deviceGroups);
            updateTopologyGraphWithDeviceGroups(deviceGroups);
          } else {
            console.log("❌ CONDITION NOT MET: No device groups available");
            console.log("deviceGroups is:", deviceGroups);
            console.log("typeof deviceGroups:", typeof deviceGroups);
            console.log(
              "Object.keys(deviceGroups):",
              Object.keys(deviceGroups || {})
            );
            console.log("Falling back to original behavior");

            // Fallback to original behavior
            if (
              selectedSubnetGroup.deviceData &&
              selectedSubnetGroup.connections
            ) {
              console.log("Using updateTopologyGraphWithSubnetGroup");
              updateTopologyGraphWithSubnetGroup(selectedSubnetGroup);
            } else {
              console.log("Using updateTopologyGraph");
              updateTopologyGraph(selectedSubnetGroup.devices || []);
            }
          }
        } else if (currentViewLevel === "devices" && selectedDeviceGroup) {
          // Show individual devices in the selected group
          console.log(
            "Showing individual devices for group:",
            selectedDeviceGroup.name
          );
          updateTopologyGraphWithDevices(selectedDeviceGroup.devices);
        } else {
          console.log("❌ View level not 'subnetgroup', falling back");
          console.log("Current view level:", currentViewLevel);
          // Fallback to original behavior
          if (
            selectedSubnetGroup.deviceData &&
            selectedSubnetGroup.connections
          ) {
            updateTopologyGraphWithSubnetGroup(selectedSubnetGroup);
          } else {
            updateTopologyGraph(selectedSubnetGroup.devices || []);
          }
        }
        console.log("=== END USEEFFECT GRAPH UPDATE DEBUG ===");
      }, 100);
    }
  }, [
    graph,
    selectedSubnetGroup,
    currentViewLevel,
    deviceGroups,
    selectedDeviceGroup,
  ]);

  // Timeout mechanism to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
        <span style={{ marginLeft: 16 }}>Loading network data...</span>
      </div>
    );
  }

  return (
    <div>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Subnet Groups */}
        <Col span={7}>
          <Card
            title={
              <span>
                <GroupOutlined style={{ marginRight: 8 }} />
                Subnet Groups
              </span>
            }
            style={{ height: "100%" }}
            styles={{
              body: {
                height: "calc(100vh - 158px)",
                overflow: "auto",
                padding: 10,
              },
            }}
            extra={
              <Flex gap={10} align="center" wrap="wrap">
                <Popconfirm
                  title="Cleanup Groups"
                  description="Remove devices from subnet groups that no longer exist in the system. This action cannot be undone."
                  onConfirm={handleCleanupGroups}
                  okText="Yes, Cleanup"
                  cancelText="Cancel"
                >
                  <Button
                    icon={<DeleteOutlined />}
                    title="Remove non-existent devices from subnet groups"
                  >
                    Cleanup
                  </Button>
                </Popconfirm>
                <Button icon={<ReloadOutlined />} onClick={refetch} />
              </Flex>
            }
          >
            {groups && groups.length > 0 ? (
              <Tree
                showIcon
                defaultExpandAll
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
                treeData={convertToTreeData(finalGroups)}
                style={{ fontSize: "14px" }}
              />
            ) : (
              <div
                style={{ textAlign: "center", padding: "20px", color: "#999" }}
              >
                <ApartmentOutlined
                  style={{ fontSize: "48px", marginBottom: "16px" }}
                />
                <div>No relm hierarchy available</div>
              </div>
            )}
          </Card>
        </Col>

        {/* Right Panel - Topology Visualization */}
        <Col span={17}>
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: 16 }}>
                <span>
                  <LaptopOutlined style={{ marginRight: 8 }} />
                  Device Topology
                  {selectedSubnetGroup && (
                    <span
                      style={{
                        marginLeft: 16,
                        color: "#722ed1",
                        fontSize: "14px",
                      }}
                    >
                      ({selectedSubnetGroup.name} -{" "}
                      {selectedSubnetGroup.devices?.length || 0} devices)
                    </span>
                  )}
                </span>

                {/* Grouping dropdown for device topology */}
                {selectedSubnetGroup && currentViewLevel === "subnetgroup" && (
                  <div
                    style={{ display: "flex", alignItems: "center", gap: 8 }}
                  >
                    <span style={{ fontSize: "12px", color: "#666" }}>
                      Group by:
                    </span>
                    <Select
                      size="small"
                      value={groupingMode}
                      onChange={setGroupingMode}
                      style={{ width: 120 }}
                      options={[
                        { value: "modelname", label: "Model Name" },
                        { value: "macprefix", label: "MAC Prefix" },
                        { value: "ipprefix", label: "IP Prefix" },
                      ]}
                    />
                  </div>
                )}
              </div>
            }
            style={{ height: "100%" }}
            styles={{ body: { padding: 5 } }}
            extra={
              <Space>
                <Tooltip title="Zoom In">
                  <Button
                    size="small"
                    icon={<ZoomInOutlined />}
                    onClick={handleZoomIn}
                  />
                </Tooltip>

                <Tooltip title="Zoom Out">
                  <Button
                    size="small"
                    icon={<ZoomOutOutlined />}
                    onClick={handleZoomOut}
                  />
                </Tooltip>

                <Tooltip title="Fit View">
                  <Button
                    size="small"
                    icon={<PicCenterOutlined />}
                    onClick={handleFitView}
                  />
                </Tooltip>

                <Tooltip title="Download">
                  <Button
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                  />
                </Tooltip>
              </Space>
            }
          >
            {/* Breadcrumb navigation */}
            {selectedSubnetGroup && breadcrumbs.length > 0 && (
              <div
                style={{
                  padding: "8px 12px",
                  borderBottom: "1px solid #f0f0f0",
                  backgroundColor: "#fafafa",
                }}
              >
                <Breadcrumb items={breadcrumbs} separator=">" />
              </div>
            )}

            <div
              id="network-topology-container"
              ref={containerRef}
              style={{
                width: "100%",
                height:
                  selectedSubnetGroup && breadcrumbs.length > 0
                    ? "calc(100vh - 190px)"
                    : "calc(100vh - 150px)",
                minHeight: "400px",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                position: "relative",
              }}
            >
              {loading && (
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    zIndex: 1000,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  <Spin size="large" />
                  <span style={{ color: "#666", fontSize: "14px" }}>
                    Loading topology...
                  </span>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NetworkTopology;
