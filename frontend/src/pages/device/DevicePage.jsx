import React, { useState, useCallback, useMemo, useEffect } from "react";
import { useThemeStore } from "../../utils/themes/useStore";
import {
  useGetInventriesQuery,
  useSendCommandMutation,
} from "../../app/services/commandApi";
import { useGetSubnetGroupsQuery } from "../../app/services/groupsApi";
import { useDispatch, useSelector } from "react-redux";
import {
  inventorySliceSelector,
  setSelectedRowKeys,
  setInventoryType,
  setScanTimerInterval,
  setScanTimeoutId,
  setSearchFilter,
  clearSelectedRowKeys,
  resetScanningStates,
  startScan,
  updateScanTimer,
} from "../../features/inventory/inventorySlice";
import { ProTable } from "@ant-design/pro-components";
import { deviceColumns } from "../../components/table-columns/device-table";
import {
  App,
  Button,
  Input,
  List,
  Segmented,
  Space,
  Table,
  Tooltip,
  Typography,
  Badge,
  Tag,
  Card,
  Row,
  Col,
  Divider,
  Flex,
} from "antd";
import DeviceContextMenu from "../../components/context-menu/device-menu";
import { useContextMenu } from "react-contexify";
import { mapDeviceDataForExportOnOff } from "../../utils/comman/dataMapping";
import ExportData from "../../components/exportData/ExportData";
import Icon, {
  CloseCircleFilled,
  CloseOutlined,
  ExclamationCircleFilled,
  PlusOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { RequestLocateDevice } from "../../features/singleDeviceConfigurations/locateDeviceSlice";
import { RequestRebootDevice } from "../../features/singleDeviceConfigurations/rebootDeviceSlice";
import { RequestEnableSNMP } from "../../features/singleDeviceConfigurations/enableSNMPDeciceSlice";
import FwUpdateModel from "../../components/device/FwUpdateModel";
import TrapSettingModel from "../../components/device/TrapSettingModel";
import SyslogSettimgModel from "../../components/device/SyslogSettimgModel";
import PortInfoModel from "../../components/device/PortInfoModel";
import { RequestSaveRunningConfig } from "../../features/singleDeviceConfigurations/saveRunningConfigSlice";
import { openNetworkSettingDrawer } from "../../features/singleDeviceConfigurations/singleNetworkSetting";
import { openSyslogSettingDrawer } from "../../features/singleDeviceConfigurations/singleSyslogSetting";
import { openTrapSettingDrawer } from "../../features/singleDeviceConfigurations/singleTrapSetting";
import { openFwUpdateDrawer } from "../../features/singleDeviceConfigurations/updateFirmwareDeviceSlice";
import NetworkSettingModel from "../../components/mdr/NetworkSettingModel";
import MdrSetLedModel from "../../components/mdr/MdrSetLedModel";
import MdrProfinetModel from "../../components/mdr/MdrProfinetModel";
import MdrConfigModel from "../../components/mdr/MdrConfigModel";
import ScanByIPRangeModel from "../../components/device/ScanByIPRangeModel";
import DeviceEditModel from "../../components/device/DeviceEditModel";
import { useTheme } from "antd-style";
import { cleanupServiceWorkers } from "../../utils/serviceWorkerUtils";

const { Title, Text } = Typography;

const DevicePage = () => {
  // All hooks must be called at the top level, in the same order every time
  const token = useTheme();
  const dispatch = useDispatch();
  const {
    inventoryType,
    changeInventoryType,
    setSelectedSubnetGroup,
    selectedSubnetGroup,
  } = useThemeStore();
  const { modal, notification } = App.useApp();
  const { show, hideAll } = useContextMenu();
  const {
    selectedRowKeys,
    isScanning,
    scanTimeRemaining,
    scanTimerInterval,
    scanTimeoutId,
    pollingInventory,
    scanStartTime,
    scanTotalDuration,
  } = useSelector(inventorySliceSelector);

  // API hooks - always called in the same order
  const {
    data = [],
    isFetching,
    refetch,
  } = useGetInventriesQuery(
    {
      inventoryType,
      groupid: selectedSubnetGroup,
    },
    {
      refetchOnMountOrArgChange: true,
      pollingInterval: pollingInventory,
    }
  );

  const { data: subnetGroups = [], refetch: refetchSubnetGroups } =
    useGetSubnetGroupsQuery(
      {},
      {
        refetchOnMountOrArgChange: true,
        pollingInterval: pollingInventory,
      }
    );

  const [sendCommand] = useSendCommandMutation();
  const [sendFwCommand, { isLoading: massUpdateLoading }] =
    useSendCommandMutation();
  const [sendCommandConfig, { isLoading: configSetLoading }] =
    useSendCommandMutation();

  // State hooks - always called in the same order
  const [contextRecord, setContextRecord] = useState({});
  const [inputSearch, setInputSearch] = useState("");
  const [fwModelOpen, setFwModelOpen] = useState(false);
  const [trapModelOpen, setTrapModelOpen] = useState(false);
  const [syslogModelOpen, setSyslogModelOpen] = useState(false);
  const [openPortInfo, setOpenPortInfo] = useState(false);
  const [openNetSetModel, setOpenNetSetModel] = useState(false);
  const [openLedSetModel, setOpenLedSetModel] = useState(false);
  const [openProfSetModel, setOpenProfSetModel] = useState(false);
  const [openMdrSetModel, setOpenMdrSetModel] = useState(false);
  const [openScanbyIP, setOpenScanbyIP] = useState(false);
  const [groupFilter, setGroupFilter] = useState(""); // Filter for device groups
  const [hoveredCard, setHoveredCard] = useState(null); // Track hovered card

  // Memoized filtered groups based on search input
  const filteredGroups = useMemo(() => {
    if (!groupFilter) return subnetGroups;

    return subnetGroups.filter((group) => {
      const searchTerm = groupFilter.toLowerCase();
      return (
        group.name.toLowerCase().includes(searchTerm) ||
        group.comment?.toLowerCase().includes(searchTerm) ||
        group.relmName?.toLowerCase().includes(searchTerm) ||
        group.regionName?.toLowerCase().includes(searchTerm) ||
        group.zoneName?.toLowerCase().includes(searchTerm) ||
        group.subnetName?.toLowerCase().includes(searchTerm)
      );
    });
  }, [subnetGroups, groupFilter]);

  // Memoized configuration objects
  const confirmConfig = useMemo(
    () => ({
      icon: null,
      className: "confirm-class",
      width: 360,
    }),
    []
  );

  // Memoized components
  const ConfirmContent = useCallback(
    ({ icon, title, text }) => (
      <Space align="center" direction="vertical" style={{ width: "100%" }}>
        <Icon
          component={icon}
          style={{
            color: token.colorWarning,
            fontSize: 64,
          }}
        />
        <Title level={4}>{title}</Title>
        <Text strong>{text}</Text>
      </Space>
    ),
    [token.colorWarning]
  );

  // Memoized filtered data - now only handles search filtering since groupid filtering is done on backend
  const filteredData = useMemo(() => {
    let result = data;

    // Filter by search input
    if (inputSearch) {
      result = result.filter((row) => {
        const searchableFields = deviceColumns().map((element) => {
          return row[element.dataIndex]
            ?.toString()
            ?.toLowerCase()
            ?.includes(inputSearch?.toLowerCase());
        });
        return searchableFields.includes(true);
      });
    }

    return result;
  }, [data, inputSearch]);

  // Event handlers with useCallback
  const handleDeleteAllDevice = useCallback(async () => {
    try {
      const command = [
        {
          command: "device delete all",
          all: true,
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
      refetchSubnetGroups();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  }, [sendCommand, notification, refetch]);

  const handleAddSSHTunnel = useCallback(
    async (mac) => {
      try {
        const command = [
          {
            command: `agent ssh reverse websrv ${mac}`,
          },
        ];
        await sendCommand(command).unwrap();
        notification.success({
          message: "no tunnel url, sent command to add tunnel!",
        });
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      }
    },
    [sendCommand, notification]
  );

  const handleDevicesReboot = useCallback(
    async (selectedKeys) => {
      if (selectedKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }

      try {
        const confirmed = await modal.confirm({
          title: "Devices Reboot",
          content: "Do you want to reboot for selected devices ?",
        });
        if (!confirmed) {
          return;
        }
        const command = selectedKeys.map((mac) => ({
          command: `reset ${mac}`,
        }));
        await sendCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
      }
    },
    [notification, modal, sendCommand, refetch, dispatch]
  );

  const handleDevicesEnableSnmp = useCallback(
    async (selectedKeys) => {
      if (selectedKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }

      try {
        const confirmed = await modal.confirm({
          title: "Enable SNMP",
          content: "Do you want to enable SNMP for selected devices ?",
        });
        if (!confirmed) {
          return;
        }
        const command = selectedKeys.map((mac) => ({
          command: `snmp enable ${mac}`,
        }));
        await sendCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
      }
    },
    [notification, modal, sendCommand, refetch, dispatch]
  );

  const handleDevicesDelete = useCallback(
    async (selectedKeys) => {
      if (selectedKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }

      try {
        const confirmed = await modal.confirm({
          title: "Delete Device",
          content: "Do you want to delete selected devices ?",
        });
        if (!confirmed) {
          return;
        }
        const command = selectedKeys.map((mac) => ({
          command: `device delete ${mac}`,
        }));
        await sendCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
      }
    },
    [notification, modal, sendCommand, refetch, dispatch]
  );

  const handleContextMenuClick = useCallback(
    (key, data, selectedKey) => {
      console.log(key, data, selectedKey);
      const {
        ipaddress,
        mac,
        netmask,
        gateway,
        hostname,
        isdhcp,
        modelname,
        tunneled_url,
        syslogSetting,
        trapSetting,
      } = data;

      switch (key) {
        case "openweb":
          window.open(`http://${ipaddress}`, "_blank");
          break;
        case "openwebtunnel":
          if (tunneled_url !== "") {
            window.open(`${tunneled_url}`, "_blank");
          } else handleAddSSHTunnel(mac);
          break;
        case "beep":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <ExclamationCircleFilled />}
                title="Confirm Beep Device"
                text="This will let device beep."
              />
            ),
            onOk() {
              dispatch(RequestLocateDevice({ mac, ipaddress }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "reboot":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <CloseCircleFilled />}
                title="Confirm Reboot Device"
                text="This will let device reboot."
              />
            ),
            onOk() {
              dispatch(RequestRebootDevice({ mac, ipaddress }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "enablesnmp":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <ExclamationCircleFilled />}
                title="Device SNMP enable"
                text="This will enable device SNMP."
              />
            ),
            onOk() {
              dispatch(RequestEnableSNMP({ mac, ipaddress }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "networkSetting":
          dispatch(
            openNetworkSettingDrawer({
              ipaddress,
              mac,
              netmask,
              gateway,
              hostname,
              new_ip_address: ipaddress,
              modelname,
              isdhcp,
            })
          );
          break;
        case "syslogSetting":
          dispatch(
            openSyslogSettingDrawer({
              mac,
              modelname,
              syslogSetting,
            })
          );
          break;
        case "trapSetting":
          dispatch(
            openTrapSettingDrawer({
              mac,
              modelname,
              trapSetting,
            })
          );
          break;
        case "uploadFirmware":
          dispatch(
            openFwUpdateDrawer({
              mac,
              modelname,
            })
          );
          break;
        case "saveConfig":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <ExclamationCircleFilled />}
                title="Device save config"
                text="This will save device."
              />
            ),
            onOk() {
              dispatch(RequestSaveRunningConfig({ mac_address: mac }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "massReboot":
          handleDevicesReboot(selectedKey);
          break;
        case "massEnablesnmp":
          handleDevicesEnableSnmp(selectedKey);
          break;
        case "deleteDevice":
          handleDevicesDelete(selectedKey);
          break;
        case "massUploadFirmware":
          setFwModelOpen(true);
          break;
        case "massTrapSetting":
          setTrapModelOpen(true);
          break;
        case "massSyslogSetting":
          setSyslogModelOpen(true);
          break;
        case "portInfo":
          setContextRecord(data);
          setOpenPortInfo(true);
          break;
        case "setNetwork":
          setContextRecord(data);
          setOpenNetSetModel(true);
          break;
        case "setLed":
          setContextRecord(data);
          setOpenLedSetModel(true);
          break;
        case "setMdr":
          setContextRecord(data);
          setOpenMdrSetModel(true);
          break;
        case "setProfinet":
          setContextRecord(data);
          setOpenProfSetModel(true);
          break;
        default:
          break;
      }
    },
    [
      modal,
      confirmConfig,
      ConfirmContent,
      dispatch,
      handleAddSSHTunnel,
      handleDevicesReboot,
      handleDevicesEnableSnmp,
      handleDevicesDelete,
      setFwModelOpen,
      setTrapModelOpen,
      setSyslogModelOpen,
      setContextRecord,
      setOpenPortInfo,
      setOpenNetSetModel,
      setOpenLedSetModel,
      setOpenMdrSetModel,
      setOpenProfSetModel,
    ]
  );

  const handleDevicesFwUpgrade = useCallback(
    async (value) => {
      if (selectedRowKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }
      try {
        const command = selectedRowKeys.map((mac) => ({
          command: `firmware update ${mac} ${value.fwUrl}`,
        }));
        await sendFwCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
        setFwModelOpen(false);
      }
    },
    [
      selectedRowKeys,
      notification,
      sendFwCommand,
      refetch,
      dispatch,
      setFwModelOpen,
    ]
  );

  const handleDevicesTrapSetting = useCallback(
    async (value) => {
      if (selectedRowKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }
      try {
        const command = selectedRowKeys.map((mac) => ({
          command: `snmp trap add ${mac} ${value.serverIp} ${value.serverPort} ${value.comString}`,
        }));
        await sendFwCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
        setTrapModelOpen(false);
      }
    },
    [
      selectedRowKeys,
      notification,
      sendFwCommand,
      refetch,
      dispatch,
      setTrapModelOpen,
    ]
  );

  const handleDevicesSyslogSetting = useCallback(
    async (value) => {
      if (selectedRowKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }
      try {
        const command = selectedRowKeys.map((mac) => ({
          command: `config syslog set ${mac} ${value.logToServer ? 1 : 0} ${
            value.serverIP
          } ${value.serverPort} ${value.logLevel} ${value.logToFlash ? 1 : 0}`,
        }));
        await sendFwCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
        setSyslogModelOpen(false);
      }
    },
    [
      selectedRowKeys,
      notification,
      sendFwCommand,
      refetch,
      dispatch,
      setSyslogModelOpen,
    ]
  );

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Handler for subnet group card selection (radio-style single selection)
  const handleSubnetGroupSelect = useCallback((groupId) => {
    setSelectedSubnetGroup(groupId);
  }, []);

  // Development helper to clean up service workers if needed
  const handleCleanupServiceWorkers = useCallback(async () => {
    if (process.env.NODE_ENV === "development") {
      try {
        await cleanupServiceWorkers();
        notification.success({
          message: "Service Workers Cleaned",
          description:
            "All service workers and caches have been cleared. Consider refreshing the page.",
        });
      } catch (error) {
        notification.error({
          message: "Cleanup Failed",
          description: "Failed to clean up service workers: " + error.message,
        });
      }
    }
  }, [notification]);

  const handleSetMdrNetwork = useCallback(
    async (values) => {
      const newIp = values.isdhcp ? "0.0.0.0" : values.newipaddress;
      const dhcp = values.isdhcp ? 1 : 0;
      try {
        const command = [
          {
            command: `config network set ${values.mac} ${values.ipaddress} ${newIp} ${values.netmask} ${values.gateway} ${values.hostname} ${dhcp}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenNetSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenNetSetModel,
      setContextRecord,
    ]
  );

  const handleSetMdrLed = useCallback(
    async (values) => {
      try {
        const command = [
          {
            command: `agent motor led set ${values.mac} ${values.led}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenLedSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenLedSetModel,
      setContextRecord,
    ]
  );

  const handleSetMdrProfinet = useCallback(
    async (values) => {
      try {
        const command = [
          {
            command: `agent motor profinet set ${values.mac} ${values.profinet}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenProfSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenProfSetModel,
      setContextRecord,
    ]
  );

  const handleSetMdrConfig = useCallback(
    async (values) => {
      const { mac, zone, mode, holding, speed, direction, level, sensor } =
        values;
      try {
        const command = [
          {
            command: `agent motor mdr set ${mac} ${zone} ${mode} ${holding} ${speed} ${direction} ${level} ${sensor}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenMdrSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenMdrSetModel,
      setContextRecord,
    ]
  );

  const handleScanIPrange = useCallback(
    async (value) => {
      try {
        const command = [
          {
            command: `scan ${value.cidr} -save`,
          },
        ];
        await sendCommand(command).unwrap();

        // If there's already an active scan, extend/restart the timer with the new timeout
        const totalTimeMs = 60000 * value.refreshTimeout;

        // Clear existing timers if any (to restart with new duration)
        if (scanTimerInterval) {
          clearInterval(scanTimerInterval);
        }
        if (scanTimeoutId) {
          clearTimeout(scanTimeoutId);
        }

        // Start/restart the scan with new timing
        dispatch(startScan({ totalDurationMs: totalTimeMs }));

        // Set up countdown timer that will persist across page navigation
        const intervalId = setInterval(() => {
          dispatch(updateScanTimer());
        }, 1000);

        dispatch(setScanTimerInterval(intervalId));

        // Set up timeout to end the scan
        const timeoutId = setTimeout(() => {
          dispatch(resetScanningStates());
          if (intervalId) {
            clearInterval(intervalId);
          }
        }, totalTimeMs);

        // Store the timeout ID in Redux for cleanup
        dispatch(setScanTimeoutId(timeoutId));

        const scanMessage = isScanning
          ? `Additional scan request sent! Extended scanning for ${value.refreshTimeout} minutes...`
          : `Scan by IP Range (CIDR) command sent! Scanning for ${value.refreshTimeout} minutes...`;

        notification.success({
          message: scanMessage,
        });

        // Store timeout ID for cleanup
        return () => {
          clearTimeout(timeoutId);
          if (intervalId) {
            clearInterval(intervalId);
          }
        };
      } catch (error) {
        dispatch(resetScanningStates());
        notification.error({
          message: error.data.error || error.data || error,
        });
      } finally {
        setOpenScanbyIP(false);
        refetch();
      }
    },
    [sendCommand, notification, setOpenScanbyIP, refetch, isScanning]
  );

  // Initialize timer on component mount if scan is active
  useEffect(() => {
    if (isScanning && !scanTimerInterval && scanStartTime) {
      // Restore timer if scan is active but timer is not running
      const intervalId = setInterval(() => {
        dispatch(updateScanTimer());
      }, 1000);

      dispatch(setScanTimerInterval(intervalId));

      // Calculate remaining time and set timeout
      const elapsed = Date.now() - scanStartTime;
      const remaining = Math.max(0, scanTotalDuration - elapsed);

      if (remaining > 0) {
        const timeoutId = setTimeout(() => {
          dispatch(resetScanningStates());
          clearInterval(intervalId);
        }, remaining);
        dispatch(setScanTimeoutId(timeoutId));
      } else {
        // Scan should have ended, reset states
        dispatch(resetScanningStates());
        clearInterval(intervalId);
      }
    }
  }, [
    isScanning,
    scanTimerInterval,
    scanStartTime,
    scanTotalDuration,
    dispatch,
  ]);

  // Cleanup timer on component unmount (but don't reset scan states)
  useEffect(() => {
    return () => {
      if (scanTimerInterval) {
        clearInterval(scanTimerInterval);
        // Don't reset scanning states here - let them persist
        dispatch(setScanTimerInterval(null));
      }
      if (scanTimeoutId) {
        clearTimeout(scanTimeoutId);
        dispatch(setScanTimeoutId(null));
      }
    };
  }, [scanTimerInterval, scanTimeoutId, dispatch]);

  // Helper function to format time remaining
  const formatTimeRemaining = useCallback((seconds) => {
    if (seconds <= 0) return "00:00";

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }, []);

  return (
    <>
      {/* Device Group Cards Section - Always Visible */}
      <Card
        title={
          <Space>
            <span>Device Groups</span>
            <Input
              placeholder="Filter groups..."
              value={groupFilter}
              onChange={(e) => setGroupFilter(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
          </Space>
        }
        style={{ marginBottom: 16 }}
        styles={{
          body: {
            paddingBlock: 16,
            paddingInline: 20,
            maxHeight: "280px",
            overflowY: "auto",
            overflowX: "hidden",
          },
        }}
        extra={
          <Flex gap={10}>
            {isScanning && scanTimeRemaining > 0 ? (
              <Tag
                key="scan-timer"
                color="processing"
                style={{
                  fontSize: "14px",
                  padding: "4px 12px",
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                }}
              >
                <span>🔄 Active Scan</span>
                <Badge
                  count={formatTimeRemaining(scanTimeRemaining)}
                  style={{
                    backgroundColor: "#52c41a",
                    fontSize: "12px",
                    fontWeight: "bold",
                  }}
                />
              </Tag>
            ) : null}
            <Tooltip key="clear-all-tooltip" title="clear all devices">
              <Button
                icon={<CloseOutlined />}
                onClick={handleDeleteAllDevice}
              />
            </Tooltip>
            <Tooltip
              key="scan-ip-tooltip"
              title={
                isScanning
                  ? "add device by ip range (extend current scan)"
                  : "add device by ip range"
              }
            >
              <Button
                data-testid="scan-ip-btn"
                icon={<PlusOutlined />}
                onClick={() => setOpenScanbyIP(true)}
              />
            </Tooltip>
            <Tooltip key="refresh-tooltip" title="Refresh">
              <Button
                icon={<ReloadOutlined />}
                onClick={() => refetchSubnetGroups()}
              />
            </Tooltip>
          </Flex>
        }
      >
        <Row gutter={[16, 16]}>
          {/* All Group Cards (including "All Devices" from backend) */}
          {filteredGroups &&
            filteredGroups.length > 0 &&
            filteredGroups.map((group) => (
              <Col key={group.id} xs={24} sm={12} md={8} lg={6} xl={4}>
                <Card
                  size="small"
                  hoverable
                  style={{
                    cursor: "pointer",
                    border:
                      selectedSubnetGroup === group.id
                        ? `2px solid ${token.colorPrimary}`
                        : `1px solid ${token.colorBorder}`,
                    borderRadius: "12px",
                    background:
                      selectedSubnetGroup === group.id
                        ? token.colorPrimaryBg
                        : token.colorBgContainer,
                    boxShadow:
                      hoveredCard === group.id
                        ? token.boxShadowTertiary
                        : selectedSubnetGroup === group.id
                        ? token.boxShadowSecondary
                        : token.boxShadow,
                    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                    transform:
                      hoveredCard === group.id
                        ? "translateY(-4px)"
                        : selectedSubnetGroup === group.id
                        ? "translateY(-2px)"
                        : "translateY(0)",
                    position: "relative",
                    overflow: "hidden",
                  }}
                  onMouseEnter={() => setHoveredCard(group.id)}
                  onMouseLeave={() => setHoveredCard(null)}
                  styles={{
                    body: {
                      padding: "16px 12px",
                      position: "relative",
                    },
                  }}
                  onClick={() => handleSubnetGroupSelect(group.id)}
                >
                  {/* Selection indicator */}
                  {selectedSubnetGroup === group.id && (
                    <div
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        height: "5px",
                        background: `linear-gradient(90deg, ${token.colorPrimary}, ${token.colorPrimaryActive})`,
                      }}
                    />
                  )}

                  <div style={{ textAlign: "center", position: "relative" }}>
                    {/* Group Name */}
                    <Typography.Text
                      strong
                      style={{
                        fontSize: "14px",
                        color:
                          selectedSubnetGroup === group.id
                            ? token.colorPrimary
                            : token.colorText,
                        display: "block",
                        marginBottom: "8px",
                        lineHeight: "1.4",
                      }}
                    >
                      {group.name}
                    </Typography.Text>

                    {/* Realm Info */}
                    {group.relmName && (
                      <Typography.Text
                        type="secondary"
                        style={{
                          fontSize: "11px",
                          display: "block",
                          marginBottom: "8px",
                          color: token.colorTextSecondary,
                          fontWeight: "500",
                        }}
                      >
                        📍 {group.relmName}
                      </Typography.Text>
                    )}

                    {/* Device Count Badge */}
                    <div
                      style={{
                        display: "inline-flex",
                        alignItems: "center",
                        gap: "4px",
                        padding: "4px 8px",
                        borderRadius: "12px",
                        background:
                          group.id === "all"
                            ? token.colorPrimaryBg
                            : token.colorSuccessBg,
                        border: `1px solid ${
                          group.id === "all"
                            ? token.colorPrimaryBorder
                            : token.colorSuccessBorder
                        }`,
                        transition: "all 0.2s ease",
                        transform:
                          hoveredCard === group.id ? "scale(1.05)" : "scale(1)",
                      }}
                    >
                      <Typography.Text
                        style={{
                          fontSize: "11px",
                          color:
                            group.id === "all"
                              ? token.colorPrimary
                              : token.colorSuccess,
                          fontWeight: "600",
                        }}
                      >
                        {group.deviceCount} devices{" "}
                        {group.id === "all" ? "" : "in group"}
                      </Typography.Text>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
        </Row>

        {(!filteredGroups || filteredGroups.length === 0) && (
          <div
            style={{ textAlign: "center", padding: "20px", marginTop: "16px" }}
          >
            <Typography.Text type="secondary">
              {groupFilter
                ? `No device groups found matching "${groupFilter}". Try a different search term.`
                : "No device groups found. Create subnet groups in Group Management to see them here."}
            </Typography.Text>
          </div>
        )}
      </Card>

      <div data-testid="device-table-div">
        <ProTable
          columns={deviceColumns(token)}
          dataSource={filteredData || []}
          rowKey="mac"
          defaultSize="small"
          loading={isFetching}
          options={{
            reload: handleRefresh,
            fullScreen: false,
            density: false,
            setting: false,
          }}
          search={false}
          toolbar={{
            search: {
              allowClear: true,
              onSearch: setInputSearch,
              onClear: () => setInputSearch(""),
            },
            actions: [
              // Timer display when scanning is active

              <Segmented
                key="inventory-type-segmented"
                options={["device", "mdr"]}
                value={inventoryType}
                onChange={(v) => {
                  changeInventoryType(v);
                  dispatch(setInventoryType(v));
                }}
              />,

              <ExportData
                key="export-data"
                Columns={deviceColumns(token)}
                DataSource={mapDeviceDataForExportOnOff(data)}
                title="Inventory_Device_List"
              />,
            ].filter(Boolean),
          }}
          pagination={{
            position: ["bottomCenter"],
            showQuickJumper: true,
            size: "default",
            total: filteredData.length,
            defaultPageSize: 10,
            pageSizeOptions: [10, 15, 20, 25],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          expandable={{
            expandedRowRender: (record) => (
              <List
                size="small"
                header={<div>Device Errors</div>}
                bordered
                dataSource={record.device_errors?.slice(0, 5) || []}
                renderItem={(item) => <List.Item>{item}</List.Item>}
              />
            ),
            rowExpandable: (record) => !!record.device_errors,
          }}
          scroll={{
            x: 1100,
          }}
          rowSelection={
            inventoryType === "mdr"
              ? undefined
              : {
                  selectedRowKeys,
                  onChange: (newSelectedRowKeys) => {
                    hideAll();
                    dispatch(setSelectedRowKeys(newSelectedRowKeys));
                  },
                  selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
                }
          }
          cardProps={{
            style: { boxShadow: token?.Card?.boxShadow },
          }}
          dateFormatter="string"
          columnsState={{
            persistenceKey: "nms-device-table",
            persistenceType: "localStorage",
          }}
          onRow={(record) => ({
            onContextMenu: async (event) => {
              if (record) {
                show({
                  id:
                    inventoryType === "device"
                      ? selectedRowKeys.length === 0
                        ? "device-menu"
                        : "mass-menu"
                      : "mdr-menu",
                  event,
                  props: {
                    record,
                    selectedRowKeys,
                  },
                });
              }
            },
          })}
        />
        <DeviceContextMenu onMenuItemClicked={handleContextMenuClick} />
        <ScanByIPRangeModel
          open={openScanbyIP}
          onOk={handleScanIPrange}
          onCancel={() => setOpenScanbyIP(false)}
        />
        <DeviceEditModel />
        <FwUpdateModel
          open={fwModelOpen}
          onCancel={() => {
            setFwModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesFwUpgrade}
          loading={massUpdateLoading}
        />
        <TrapSettingModel
          open={trapModelOpen}
          onCancel={() => {
            setTrapModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesTrapSetting}
          loading={massUpdateLoading}
        />
        <SyslogSettimgModel
          open={syslogModelOpen}
          onCancel={() => {
            setSyslogModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesSyslogSetting}
          loading={massUpdateLoading}
        />
        <PortInfoModel
          open={openPortInfo}
          onCancel={() => {
            setOpenPortInfo(false);
            setContextRecord({});
          }}
          recordData={contextRecord}
        />
        <NetworkSettingModel
          record={contextRecord}
          open={openNetSetModel}
          onCancel={() => {
            setOpenNetSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrNetwork}
          loading={configSetLoading}
        />
        <MdrSetLedModel
          record={contextRecord}
          open={openLedSetModel}
          onCancel={() => {
            setOpenLedSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrLed}
          loading={configSetLoading}
        />
        <MdrProfinetModel
          record={contextRecord}
          open={openProfSetModel}
          onCancel={() => {
            setOpenProfSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrProfinet}
          loading={configSetLoading}
        />
        <MdrConfigModel
          record={contextRecord}
          open={openMdrSetModel}
          onCancel={() => {
            setOpenMdrSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrConfig}
          loading={configSetLoading}
        />
      </div>
    </>
  );
};

export default DevicePage;
