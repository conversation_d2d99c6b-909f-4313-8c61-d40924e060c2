import {
  ApartmentOutlined,
  ApiOutlined,
  ClusterOutlined,
  CodeOutlined,
  DashboardOutlined,
  DesktopOutlined,
  GlobalOutlined,
  GroupOutlined,
  KeyOutlined,
  ShareAltOutlined,
  SnippetsOutlined,
  UsergroupAddOutlined,
} from "@ant-design/icons";

const routes = ({ idps = false }) => ({
  route: {
    path: "/",
    routes: [
      {
        path: "/dashboard",
        name: "Dashboard",
        icon: <DashboardOutlined />,
        routes: [
          { path: "/dashboard/device", name: "Device" },
          idps && { path: "/dashboard/idps", name: "IDPS" },
        ],
      },
      {
        path: "/devices",
        name: "Devices",
        icon: <DesktopOutlined />,
      },
      {
        path: "/groupmanagement",
        name: "Group Management",
        icon: <GroupOutlined />,
      },
      {
        path: "/scripts",
        name: "<PERSON>rip<PERSON>",
        icon: <CodeOutlined />,
      },
      {
        path: "/topology",
        name: "Topology",
        icon: <ApartmentOutlined />,
      },
      {
        path: "/hirarchicaltopology",
        name: "Hierarchical Topology",
        icon: <ShareAltOutlined />,
      },
      {
        path: "/mibbrowser",
        name: "MIB Browser",
        icon: <GlobalOutlined />,
      },

      {
        path: "/usermanagement",
        name: "User Management",
        icon: <UsergroupAddOutlined />,
      },

      {
        path: "/eventlogs",
        name: "Logs",
        icon: <SnippetsOutlined />,
      },
      {
        path: "/clusterinfo",
        name: "Cluster Info",
        icon: <ClusterOutlined />,
      },
      {
        path: "/tunnels",
        name: "Tunnel",
        icon: <ApiOutlined />,
      },
      {
        path: "/key-store",
        name: "Key Store",
        icon: <KeyOutlined />,
      },
    ],
  },
  location: {
    pathname: "/",
  },
});
export default routes;
